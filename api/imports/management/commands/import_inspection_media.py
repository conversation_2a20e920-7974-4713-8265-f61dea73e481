from pathlib import Path

import httpx
from azure.storage.blob import BlobClient
from django.conf import settings
from django.core.management import CommandError
from vapar.clients import api

from ._base_import_command import BaseImportCommand


def _format_file_size(size_bytes: int) -> str:
    if size_bytes < 1024:
        return f"{size_bytes}B"
    elif size_bytes < 1024**2:
        return f"{size_bytes / 1024:.2f}KB"
    elif size_bytes < 1024**3:
        return f"{size_bytes / 1024 ** 2:.2f}MB"
    else:
        return f"{size_bytes / 1024 ** 3:.2f}GB"


class Command(BaseImportCommand):
    help = """
    Start a set of inspection media imports for videos in a local folder. The folder should contain video files
    named as {file_id}.{extension}, where {file_id} is the ID of the file record it should be associated with.
    This will not succeed for video file records that have already been uploaded.
    """

    def add_arguments(self, parser):
        parser.add_argument("--org-id", type=int, help="ID of the organisation to import into", required=True)
        parser.add_argument("--folder", required=True, type=Path, help="Folder containing video files to import.")
        parser.add_argument(
            "--api-url",
            type=str,
            help="Override the API URL. Default is from VAPAR_API_BASE_URL env var",
            required=False,
        )
        parser.add_argument(
            "--api-key",
            type=str,
            help="Override the API secret key. Default is from VAPAR_API_SECRET_KEY env var",
            required=False,
        )

    def get_upload_url(
        self, api_settings: api.VaparAPISettings, org_id: int, file_id: int, file_path: Path
    ) -> str | None:
        headers = {"X-Api-Key": api_settings.secret_key, "X-Target-Org-Id": str(org_id)}
        file_size = file_path.stat().st_size
        formatted_size = _format_file_size(file_size)
        res = httpx.post(
            f"{api_settings.base_url}/api/v3/files/{file_id}/upload",
            headers=headers,
            json={
                "fileSize": formatted_size,
                "filename": file_path.name,
            },
        )
        if not res.is_success:
            self.stdout.write(self.style.ERROR(f"Failed to fetch upload url for {file_path.name}"))
            return None
        return res.json()["blobUrlWithSas"]

    def handle(self, *args, **options):
        folder: Path = options["folder"]
        org_id = options["org_id"]
        base_url = options.get("api_url")
        api_key = options.get("api_key")

        provided = {}
        if base_url is not None:
            provided["base_url"] = base_url
        if api_key is not None:
            provided["api_key"] = api_key
        api_settings = api.VaparAPISettings(**provided)

        if not folder.is_absolute():
            folder = settings.BASE_DIR / folder

        if not folder.is_dir():
            raise CommandError(f"Path is not a folder: {folder}")

        files: list[Path] = list(folder.glob("*"))
        try:
            file_ids = [int(f.stem) for f in files]
        except ValueError:
            raise CommandError("All files in the folder must have a numeric ID as their name.")

        self.stdout.write(f"Found {len(files)} files in {folder} with IDs: {file_ids}")

        import_ids = []
        failed_files = []
        for file_path, file_id in zip(files, file_ids):
            upload_url = self.get_upload_url(api_settings, org_id, file_id, file_path)
            if upload_url is None:
                self.stdout.write(self.style.ERROR(f"Failed to fetch upload url for file id {file_id}"))
                failed_files.append(file_path)
                continue

            self.stdout.write(f"Uploading {file_path.name} to {upload_url}")
            blob = BlobClient.from_blob_url(upload_url)
            with file_path.open(mode="rb") as f:
                blob.upload_blob(f)
            self.stdout.write(self.style.SUCCESS(f"Successfully uploaded {file_path.name} to {upload_url}"))

            import_id = self.create_import(
                api_settings=api_settings,
                org_id=org_id,
                import_type="IV",
                payload={
                    "fileId": file_id,
                },
            )
            import_ids.append(import_id)
            self.stdout.write(self.style.SUCCESS(f"Import created with ID: {import_id}"))

        self.stdout.write(self.style.SUCCESS(f"Created {len(import_ids)} imports"))
        self.stdout.write(self.style.SUCCESS(f"Import IDs: {', '.join(str(imp) for imp in import_ids)}"))
        if failed_files:
            self.stdout.write(
                self.style.ERROR(
                    f"Failed to upload {len(failed_files)} files: {', '.join(str(f) for f in failed_files)}"
                )
            )
        else:
            self.stdout.write(self.style.SUCCESS("All files uploaded successfully"))
