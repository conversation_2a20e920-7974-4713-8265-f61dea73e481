from pyexpat import ExpatError
from typing import Binary<PERSON>, TYPE_CHECKING

import pandas as pd
import xmltodict
from vapar.constants.conversion import STANDARD_HEADER_MAPPINGS
from vapar.constants.imports import ImportInspectionFileFormatEnum
from vapar.constants.pipes import Inspection<PERSON><PERSON>er<PERSON>num, StandardEnum
from vapar.core.imports import AnyInspectionImportPayload

from .errors import ErrorCodes, ImportValidationError
from .utils import validate_column_length, validate_choice_field, validate_columns_exist
from api.base.models import Header
from api.defects.models import Standard

if TYPE_CHECKING:
    from api.imports.models import ImportFile


MSCC5_XML_HEADER_INSP_REQUIRED_FIELDS = {
    "PipelineLengthRef",
    "Date",
    "Direction",
    "LengthSurveyed",
}
MSCC5_XML_HEADER_ASSET_REQUIRED_FIELDS = {
    "LocationStreet",
    "LocationTown",
    "Material",
    "HeightDiameter",
    "UpstreamNode",
    "DownstreamNode",
}
MSCC5_XML_HEADER_ALL_REQUIRED_FIELDS = MSCC5_XML_HEADER_INSP_REQUIRED_FIELDS | MSCC5_XML_HEADER_ASSET_REQUIRED_FIELDS

MIN_ASSET_VALUE_LEN = 1
MAX_ASSET_VALUE_LEN = 255
MIN_INSP_VALUE_LEN = 1
MAX_INSP_VALUE_LEN = 1_000


def validate_inspections_import(
    file_obj: "ImportFile",
    file_content: BinaryIO,
) -> list[ImportValidationError]:
    """
    Validate an inspections import file, returning a list of encountered errors.

    If the file is valid, returns an empty list.
    """

    payload = file_obj.import_operation.parsed_payload.root
    assert isinstance(payload, AnyInspectionImportPayload), "Should be validated as correct payload type by serializer"

    match payload.root.format:
        case ImportInspectionFileFormatEnum.MSCC5_DEFECT_XML:
            return validate_mscc5_defect_xml(file_obj, file_content)
        case _:
            raise NotImplementedError(
                f"Validation for inspection file format '{payload.root.format}' is not implemented"
            )


def validate_mscc5_defect_xml(file_obj: "ImportFile", file_content: BinaryIO) -> list[ImportValidationError]:
    """
    Validate an MSCC5 defect XML file, returning a list of encountered errors.

    If the file is valid, returns an empty list.
    """
    try:
        root = xmltodict.parse(file_content)
    except ExpatError:
        return [
            ImportValidationError(
                file_id=str(file_obj.id),
                description="File could not be parsed as XML",
                code=ErrorCodes.UNPARSEABLE,
            )
        ]

    if "SurveyGroup" not in root:
        return [
            ImportValidationError(
                file_id=str(file_obj.id),
                description="Missing 'SurveyGroup' element",
                code=ErrorCodes.UNPARSEABLE,
            )
        ]
    survey_group = root["SurveyGroup"] or {}
    surveys = survey_group.get("Survey")
    if surveys is None:
        return [
            ImportValidationError(
                file_id=str(file_obj.id),
                description="Missing 'Survey' elements",
                code=ErrorCodes.UNPARSEABLE,
            )
        ]
    if not isinstance(surveys, list):
        surveys = [surveys]  # Unwrap when there's only one survey

    errors = []

    if any(not isinstance(s, dict) or not isinstance(s.get("Header"), dict) for s in surveys):
        return [
            ImportValidationError(
                file_id=str(file_obj.id),
                description="Missing 'Header' element in 'Survey'",
                code=ErrorCodes.UNPARSEABLE,
            )
        ]

    headers_df = pd.DataFrame.from_records((s["Header"] for s in surveys))
    headers_df = headers_df.astype(str).where(headers_df.notna())

    header_errors = validate_mscc5_defect_xml_header_fields(headers_df, str(file_obj.id))
    errors.extend(header_errors)

    # TODO: Validate observations when implemented

    return errors


def validate_mscc5_defect_xml_header_fields(headers_df: pd.DataFrame, file_id: str) -> list[ImportValidationError]:
    missing_col_errors = validate_columns_exist(headers_df, MSCC5_XML_HEADER_ALL_REQUIRED_FIELDS, file_id)
    if missing_col_errors:
        return missing_col_errors

    errors = []

    # Validate not empty
    for col in MSCC5_XML_HEADER_ALL_REQUIRED_FIELDS:
        empty_rows = headers_df[headers_df[col].isna() | (headers_df[col] == "")]
        if not empty_rows.empty:
            errors.append(
                ImportValidationError(
                    file_id=file_id,
                    description=f"Column '{col}' cannot be empty",
                    code=ErrorCodes.BAD_VALUE,
                    row_numbers=list(empty_rows.index + 1),  # Display as 1-indexed row numbers
                )
            )

    # Validate length in range
    for col in MSCC5_XML_HEADER_INSP_REQUIRED_FIELDS:
        insp_len_errors = validate_column_length(headers_df, col, file_id, MIN_INSP_VALUE_LEN, MAX_INSP_VALUE_LEN)
        errors.extend(insp_len_errors)
    for col in MSCC5_XML_HEADER_ASSET_REQUIRED_FIELDS:
        asset_len_errors = validate_column_length(headers_df, col, file_id, MIN_ASSET_VALUE_LEN, MAX_ASSET_VALUE_LEN)
        errors.extend(asset_len_errors)

    # Validate 'Direction' and 'Material' fields
    allowed_dir_values = set(STANDARD_HEADER_MAPPINGS[InspectionHeaderEnum.DIRECTION][StandardEnum.MSCC5].values())
    dir_errors = validate_choice_field(headers_df, "Direction", allowed_dir_values, file_id, allow_null=False)
    errors.extend(dir_errors)

    mscc5 = Standard.objects.get(name="MSCC5")
    allowed_material_values = set(
        (
            mscc5.standardheader_set.filter(header__name="Material", header__type=Header.HeaderType.ASSET)
            .get()
            .options_selections
        )
    )
    material_errors = validate_choice_field(headers_df, "Material", allowed_material_values, file_id, allow_null=False)
    errors.extend(material_errors)

    # Validate 'Date' field is in the format 'YYYY-MM-DD'
    affected_date_rows = headers_df[pd.to_datetime(headers_df["Date"], errors="coerce", format="%Y-%m-%d").isna()]
    if not affected_date_rows.empty:
        errors.append(
            ImportValidationError(
                file_id=file_id,
                description="'Date' must be in the format 'YYYY-MM-DD'",
                code=ErrorCodes.BAD_VALUE,
                row_numbers=list(affected_date_rows.index + 1),
            )
        )

    # Validate LengthSurveyed is convertible to a number
    affected_length_rows = headers_df[pd.to_numeric(headers_df["LengthSurveyed"], errors="coerce").isna()]
    if not affected_length_rows.empty:
        errors.append(
            ImportValidationError(
                file_id=file_id,
                description="'LengthSurveyed' must be a number",
                code=ErrorCodes.BAD_VALUE,
                row_numbers=list(affected_length_rows.index + 1),
            )
        )

    affected_diameter_rows = headers_df[pd.to_numeric(headers_df["HeightDiameter"], errors="coerce").isna()]
    if not affected_diameter_rows.empty:
        errors.append(
            ImportValidationError(
                file_id=file_id,
                description="'HeightDiameter' must be a number",
                code=ErrorCodes.BAD_VALUE,
                row_numbers=list(affected_diameter_rows.index + 1),
            )
        )

    return errors
