import re
import uuid
from datetime import datetime
from typing import Any, ClassVar

from django.core.cache import caches
from pydantic import (
    BaseModel,
    ConfigDict,
    Field,
    SerializationInfo,
    field_validator,
    field_serializer,
    ValidationInfo,
    model_validator,
)
from vapar.constants.conversion import StandardValueConverter
from vapar.constants.pipes import PipeTypeEnum

ASSET_CACHE = caches["assets"]


class LocationInfo(BaseModel):
    """
    Representation of location information for an asset

    :param street: the street address
    :param town: the town/city address
    """

    model_config = ConfigDict(extra="ignore", populate_by_name=True)

    street: str | None = Field("", alias="LocationStreet")
    town: str | None = Field("", alias="LocationTown")
    country: str | None = ""

    @property
    def value(self):
        return ", ".join(filter(None, [self.street, self.town]))

    @field_validator("street", "town", mode="before")
    @classmethod
    def validate_inputs(cls, val: str | None, info: ValidationInfo) -> str | None:
        if isinstance(val, str):
            return val.strip()
        return val

    @classmethod
    def from_details(cls, data):
        street = data.get("LocationStreet", "")
        town = data.get("LocationTown", "")
        country = ""

        if "," in street and town == "":
            location = street.split(",")
            street = location[0]
            town = location[1]
            if len(location) > 2:
                country = location[2]

        return cls(street=street, town=town, country=country)


class AssetModel(BaseModel):
    """
    Representation of an asset decoupled from the database
    """

    model_config = ConfigDict(extra="allow", populate_by_name=True)

    uuid: uuid.UUID
    type: str
    organisation: int
    standard: int | None = None
    created_at: str = Field(..., alias="createdAt")
    asset_id: str | None = Field(None, alias="AssetID")
    upstream_node: str | None = Field(None, alias="UpstreamNode")
    downstream_node: str | None = Field(None, alias="DownstreamNode")
    diameter: int | None = Field(None, alias="HeightDiameter")
    material: str | None = Field(None, alias="Material")
    location_street: str | None = Field(None, alias="LocationStreet")
    location_town: str | None = Field(None, alias="LocationTown")
    pipe_type: PipeTypeEnum = Field(PipeTypeEnum.SEWER, alias="UseOfDrainSewer")
    repair_completed_date: datetime | None = Field(None, alias="YearRenewed")
    inspections_count: int = Field(0, alias="inspectionsCount")

    extra_fields: dict[str, Any] = Field({}, alias="extraFields")

    def model_post_init(self, __context: Any):
        """Set extra fields that were passed to the model"""

        defined_fields = self.model_fields.keys()
        alias_fields = {v.alias for v in self.model_fields.values() if v.alias}
        expected_fields = set(defined_fields) | alias_fields
        data = self.__pydantic_extra__ or {}
        self.extra_fields.update({k: v for k, v in data.items() if k not in expected_fields})

        # Remove extra fields from top level attributes
        for name in self.extra_fields:
            if hasattr(self, name):
                delattr(self, name)

    @field_validator("diameter", mode="before")
    @classmethod
    def validate_diameter(cls, val: int | str) -> int | None:
        if isinstance(val, str):
            try:
                val = int(re.sub(r"[a-z\s]", "", val))
            except ValueError:
                val = 0
        return val

    @field_validator("pipe_type", mode="before")
    @classmethod
    def validate_pipe_type(cls, val: str, info: ValidationInfo) -> PipeTypeEnum:
        try:
            pipe_type = PipeTypeEnum(val)
        except ValueError:
            # try standard value conversion if standard context is provided
            if (context := info.context) and val:
                standard = context.get("standard")
                converter = StandardValueConverter(header="UseOfDrainSewer", standard=standard)
                pipe_type = converter.get_common_value(val)
                if not isinstance(pipe_type, PipeTypeEnum):
                    pipe_type = PipeTypeEnum.SEWER
            else:
                # set default value
                pipe_type = PipeTypeEnum.SEWER

        return pipe_type

    @field_serializer("pipe_type")
    @classmethod
    def serialize_pipe_type(self, value: PipeTypeEnum, info: SerializationInfo) -> str:
        if context := info.context:
            standard = context.get("standard")
            converter = StandardValueConverter(header="UseOfDrainSewer", standard=standard)
            value = converter.get_standard_value(value)
            return value.value
        return value.value

    @field_serializer("diameter")
    def serialize_diameter(self, value):
        diameter = value
        if isinstance(diameter, str):
            diameter = "".join(filter(str.isdigit, diameter))

        diameter = int(diameter) if diameter else 0

        return diameter

    @field_serializer("asset_id")
    def serialize_asset_id(self, value):
        if value is None:
            return ""

        return value


class AssetPatchModel(BaseModel):

    model_config = ConfigDict(extra="ignore")

    type: str | None = None
    asset_id: str | None = Field(None, alias="AssetID")
    upstream_node: str | None = Field(None, alias="UpstreamNode")
    downstream_node: str | None = Field(None, alias="DownstreamNode")
    diameter: int | None = Field(None, alias="HeightDiameter")
    material: str | None = Field(None, alias="Material")
    location_street: str | None = Field(None, alias="LocationStreet")
    location_town: str | None = Field(None, alias="LocationTown")

    @model_validator(mode="after")
    def prevent_explicit_none(self, values: ValidationInfo):
        for key in self.model_fields_set:
            assert getattr(self, key) is not None, f"{key} can be excluded from the request body but not set to None"


class AssetPostModel(AssetPatchModel):
    organisation: int
    standard: int | None = None
    use_of_drain_sewer: PipeTypeEnum = Field(PipeTypeEnum.SEWER, alias="UseOfDrainSewer")


class AssetMatchOrCreateRequest(BaseModel):
    _unique_headers: ClassVar[set[str]] = {"AssetID"}
    _identifiable_headers: ClassVar[set[str]] = {"UpstreamNode", "DownstreamNode", "LocationStreet", "LocationTown"}

    standard_id: int = Field(..., alias="standardID")
    organisation_id: int | None = Field(None, alias="orgID")
    asset_id: str | None = Field(None, alias="AssetID")
    upstream_node: str | None = Field(None, alias="UpstreamNode")
    downstream_node: str | None = Field(None, alias="DownstreamNode")
    length: float | None = Field(None, alias="ExpectedLength")
    diameter: int | None = Field(None, alias="HeightDiameter")
    material: str | None = Field(None, alias="Material")
    location_street: str | None = Field(None, alias="LocationStreet")
    location_town: str | None = Field(None, alias="LocationTown")
    pipe_type: str | None = Field(None, alias="UseOfDrainSewer")
    imported_from_id: uuid.UUID | None = Field(None, alias="importedFromID")

    def asset_value_fields(self) -> dict[str, Any]:
        return self.model_dump(by_alias=True, exclude={"standard_id", "organisation_id", "imported_from_id"})

    @property
    def has_required_for_match(self) -> bool:
        keys = {k for k, v in self.asset_value_fields().items() if v is not None}
        return self._unique_headers.issubset(keys) or self._identifiable_headers.issubset(keys)


class AssetMatchOrCreateResponse(BaseModel):
    is_matched: bool = Field(..., alias="isMatched")
    asset: AssetModel
