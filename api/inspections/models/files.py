from django.conf import settings
from django.core.validators import MinV<PERSON>ueValidator, MaxValueValidator
from django.db import models
from django.db.models import CheckConstraint, Q, Prefetch
from vapar.constants.processing import ProcessingStatusReasonEnum, ProcessingStatusEnum

from api.common.enums import ProcessingRetryableEnum
from api.common.errors import CustomValidationError
from api.defects.models import DefectModelList, DefectScores, Standard

from api.organisations.models import Organisations
from api.users.models import CustomUser


class FileListQuerySet(models.QuerySet):
    def for_org(self, org: Organisations):
        return self.filter(Q(upload_org=org) | Q(target_org=org))


class FileList(models.Model):
    filename = models.CharField(max_length=200, blank=True)
    url = models.CharField(max_length=1000, blank=True)
    file_size = models.CharField(max_length=200, blank=True)
    file_type = models.CharField(max_length=50, blank=True)
    created_time = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    upload_user = models.CharField(max_length=200, blank=False)
    target_org = models.ForeignKey(Organisations, on_delete=models.SET_NULL, null=True, related_name="target_org")
    upload_org = models.ForeignKey(Organisations, on_delete=models.SET_NULL, null=True, related_name="upload_org")
    total_frames = models.IntegerField(default=0)
    hidden = models.BooleanField(default=False)
    job = models.ForeignKey("inspections.Jobs", on_delete=models.SET_NULL, null=True)
    results_file = models.ForeignKey("inspections.ResultsFile", on_delete=models.SET_NULL, null=True)
    request_endpoint = models.CharField(max_length=500, blank=True, default="")
    job_tree = models.ForeignKey("inspections.JobsTree", related_name="jt", on_delete=models.SET_NULL, null=True)
    play_url = models.CharField(max_length=1000, blank=True)
    uploaded_by = models.ForeignKey(CustomUser, on_delete=models.SET_NULL, null=True)
    storage_region = models.CharField(max_length=20, null=False, default="AU")
    upload_completed = models.BooleanField(default=False)
    upload_completed_time = models.DateTimeField(null=True, blank=True)
    processing_started_time = models.DateTimeField(null=True, blank=True)
    processing_completed_time = models.DateTimeField(null=True, blank=True)

    objects = FileListQuerySet.as_manager()

    def is_accessible_by_org(self, org: Organisations) -> bool:
        """
        :returns: Whether the file is accessible by the given organisation
        """
        return self.target_org == org or self.upload_org == org

    class Meta:
        db_table = "service_filelist"


class VideoFrameQuerySet(models.QuerySet):
    def only_reported(self):
        """
        Only return reportable frames
        """
        return self.filter(is_hidden=False).filter(defect_scores__is_shown=True)

    def with_defect_score_severity(self):
        """
        Prefetch related defect scores, annotated with severity
        """

        defects = DefectScores.objects.with_severity().all()
        qs = self.prefetch_related(Prefetch("defect_scores", queryset=defects))
        return qs


class VideoFrames(models.Model):
    image_location = models.CharField(max_length=200, blank=True)
    parent_video = models.ForeignKey(FileList, on_delete=models.CASCADE)
    frame_id = models.IntegerField(default=0)
    time_reference = models.CharField(max_length=20, default="", blank=True, null=True)
    chainage = models.CharField(max_length=10, default="0", blank=True, null=True)
    chainage_number = models.FloatField(
        validators=[MinValueValidator(0.0), MaxValueValidator(settings.MAX_CHAINAGE)],
        default=0.0,
        blank=False,
        null=False,
    )
    class_label = models.CharField(max_length=200, blank=True)
    class_certainty = models.DecimalField(max_digits=5, decimal_places=2, blank=True)
    duplicate_of = models.IntegerField(default=-1)
    all_class_breakdown = models.CharField(max_length=1000, blank=True)
    fix_user = models.CharField(max_length=200, blank=True, null=True)
    original_fixed_label = models.CharField(max_length=200, blank=True, null=True)
    is_accepted = models.BooleanField(default=False)
    reviewed_by = models.CharField(max_length=200, blank=False, null=True)
    review_timestamp = models.DateTimeField(blank=True, null=True)
    is_hidden = models.BooleanField(default=False)
    at_joint = models.BooleanField(blank=True, null=True, default=None)
    at_clock = models.IntegerField(blank=True, null=True, default=None)
    to_clock = models.IntegerField(blank=True, null=True, default=None)
    cont_defect_start = models.BooleanField(default=False)
    cont_defect_end = models.DecimalField(blank=True, null=True, default=None, max_digits=10, decimal_places=2)
    quantity1_value = models.DecimalField(blank=True, null=True, default=None, max_digits=10, decimal_places=2)
    quantity1_units = models.CharField(max_length=200, blank=True, null=True, default=None)
    quantity2_value = models.DecimalField(blank=True, null=True, default=None, max_digits=10, decimal_places=2)
    quantity2_units = models.CharField(max_length=200, blank=True, null=True, default=None)
    is_matched = models.BooleanField(blank=True, null=True, default=None)
    remarks = models.TextField(blank=True, null=True)
    defect_model = models.ForeignKey(DefectModelList, on_delete=models.SET_NULL, null=True)
    defect_scores = models.ForeignKey(DefectScores, on_delete=models.SET_NULL, null=True)

    objects = VideoFrameQuerySet.as_manager()

    class Meta:
        ordering = ["frame_id"]
        db_table = "service_videoframes"

        constraints = [
            CheckConstraint(
                check=Q(at_clock__gte=1, at_clock__lte=12) | Q(at_clock__isnull=True),
                name="video_frame_at_clock_range_constraint",
            ),
            CheckConstraint(
                check=Q(to_clock__gte=1, to_clock__lte=12) | Q(to_clock__isnull=True),
                name="video_frame_to_clock_range_constraint",
            ),
        ]

    def validate(self):
        errors = []

        # Common validation checks
        if self.defect_scores.clock_position_required and self.at_clock is None:
            error = CustomValidationError(
                title="Clock Start",
                message="'Clock Start' value missing for frame at chainage {}".format(self.chainage_number),
                field_name="atClock",
                entity="defect",
                metadata={"frame_id": self.id},
            )
            errors.append(error.serialize())

        if self.defect_scores.clock_spread_possible and self.to_clock is not None and self.at_clock is None:
            error = CustomValidationError(
                title="Clock Start",
                message="'Clock Start' value missing for frame at chainage {}".format(self.chainage_number),
                field_name="atClock",
                entity="defect",
                metadata={"frame_id": self.id},
            )
            errors.append(error.serialize())

        if not self.defect_scores.clock_spread_possible and self.to_clock is not None:
            error = CustomValidationError(
                title="Clock End",
                message="'Clock End' value should be empty for frame at chainage {}".format(self.chainage_number),
                field_name="toClock",
                entity="defect",
                metadata={"frame_id": self.id},
            )
            errors.append(error.serialize())

        if not self.defect_scores.at_joint_required and self.at_joint:
            error = CustomValidationError(
                title="At Joint",
                message="'At Joint' should be unchecked for frame at chainage {}".format(self.chainage_number),
                field_name="atJoint",
                entity="defect",
                metadata={"frame_id": self.id},
            )
            errors.append(error.serialize())

        if self.defect_scores.percentage_required:
            if self.quantity1_units != "%":
                error = CustomValidationError(
                    title="Quantity 1 Units",
                    message="'Quantity 1 Units' value should be '%' for frame at chainage {}".format(
                        self.chainage_number
                    ),
                    field_name="quantity1Units",
                    entity="defect",
                    metadata={"frame_id": self.id},
                )
                errors.append(error.serialize())

            if self.quantity1_value == "" or self.quantity1_value is None:
                error = CustomValidationError(
                    title="Quantity 1",
                    message="'Quantity 1' value missing for frame at chainage {}".format(self.chainage_number),
                    field_name="quantity1Value",
                    entity="defect",
                    metadata={"frame_id": self.id},
                )
                errors.append(error.serialize())

        return errors


class ProcessingList(models.Model):
    RETRYABLE_STATUS_REASONS = {
        ProcessingStatusReasonEnum.GENERIC_ERROR,
        ProcessingStatusReasonEnum.TIMEOUT_WHILE_WAITING,
        ProcessingStatusReasonEnum.TIMEOUT_WHILE_PROCESSING,
    }

    MAX_RETRIES = 3

    filename = models.CharField(max_length=200, blank=True)
    file_size = models.CharField(max_length=200, blank=True)

    status = models.CharField(
        max_length=200,
        blank=True,
        default=ProcessingStatusEnum.UPLOADING,
        choices=ProcessingStatusEnum.as_choices(),
    )
    status_reason = models.CharField(
        max_length=2,
        blank=True,
        null=True,
        choices=ProcessingStatusReasonEnum.as_choices(),
        default=None,
    )
    times_retried = models.PositiveIntegerField(default=0)

    created_time = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    upload_user = models.CharField(max_length=200, blank=False)

    target_org = models.ForeignKey(Organisations, on_delete=models.SET_NULL, null=True)
    associated_file = models.OneToOneField(
        FileList, on_delete=models.SET_NULL, null=True, related_name="processing_record"
    )

    request_endpoint = models.CharField(max_length=500, blank=True, default="")
    manual_qa_required = models.BooleanField(default=False)
    sewer_data = models.BooleanField(default=True)
    standard_key = models.ForeignKey(Standard, on_delete=models.SET_NULL, null=True)
    upload_completed = models.BooleanField(default=False)

    @property
    def retryable_state(self) -> ProcessingRetryableEnum:
        if self.times_retried >= self.MAX_RETRIES:
            return ProcessingRetryableEnum.MAX_RETRIES_EXCEEDED

        if (
            self.status == ProcessingStatusEnum.FAILED_TO_PROCESS
            and self.status_reason in self.RETRYABLE_STATUS_REASONS
        ):
            return ProcessingRetryableEnum.AVAILABLE

        return ProcessingRetryableEnum.BAD_STATE

    @property
    def is_retryable(self) -> bool:
        return self.retryable_state == ProcessingRetryableEnum.AVAILABLE

    def retry(self):
        """
        Mark the processing record as retrying a processing attempt.
        """
        self.times_retried += 1
        self.status = ProcessingStatusEnum.WAITING_TO_PROCESS
        self.status_reason = None
        self.save()

    class Meta:
        db_table = "service_processinglist"


class ResultsFile(models.Model):
    filename = models.CharField(max_length=200, blank=True)
    file_location = models.CharField(max_length=1000, blank=True)
    file_size = models.CharField(max_length=200, blank=True)
    organisation = models.ForeignKey(Organisations, on_delete=models.SET_NULL, null=True)
    header_only = models.BooleanField(default=False)

    class Meta:
        db_table = "service_resultsfile"


class ResultsFileAssets(models.Model):
    results_file = models.ForeignKey(ResultsFile, on_delete=models.SET_NULL, null=True)
    associated_file = models.ForeignKey(FileList, on_delete=models.SET_NULL, null=True)
    video_filename = models.CharField(max_length=200, blank=True)
    asset_id = models.CharField(max_length=100, blank=True, null=True)
    start_node = models.CharField(max_length=100, blank=True, null=True)
    end_node = models.CharField(max_length=100, blank=True, null=True)
    direction = models.CharField(max_length=100, blank=True, null=True)
    chainage = models.CharField(max_length=10, default="0", blank=True, null=True)
    date_captured = models.DateField(blank=True, null=True)

    class Meta:
        db_table = "service_resultsfileassets"


class ResultsFileDefects(models.Model):
    results_file_asset = models.ForeignKey(ResultsFileAssets, on_delete=models.SET_NULL, null=True)
    associated_video_frame = models.ForeignKey(VideoFrames, on_delete=models.SET_NULL, null=True)
    time_reference = models.CharField(max_length=20, default="", blank=True, null=True)
    chainage_number = models.DecimalField(max_digits=10, decimal_places=2, blank=True, null=True)
    comment = models.CharField(max_length=200, blank=True)
    defect_code = models.CharField(max_length=200, blank=True)

    class Meta:
        db_table = "service_resultsfiledefects"
