from django.db import models
from treebeard.mp_tree import MP_Node

from api.defects.models import Standard
from api.inspections.models.files import FileList
from api.organisations.models import Organisations


# Table not used - to be removed with database redesign (referenced in map point list and filelist)
class Jobs(models.Model):
    Organisation = models.ForeignKey(Organisations, on_delete=models.SET_NULL, null=True)
    job_name = models.CharField(max_length=200, blank=False)
    Created_by = models.CharField(max_length=200, blank=False)
    Created_date = models.DateTimeField(blank=False)
    pipe_type_sewer = models.BooleanField(null=True, blank=True)
    standard_key = models.ForeignKey(Standard, on_delete=models.SET_NULL, null=True)

    def __str__(self):
        return str(self.job_name)

    class Meta:
        db_table = "service_jobs"


class JobsTree(MP_Node):
    """
    Represents a node in a folder tree

    The 'primary_org' is the organisation that the folder tree is rooted in. (It should only be set on root nodes)
    All non contractor folders are owned by this organisation.

    The 'secondary_org' is the contractor organisation that owns a particular folder and its children
    """

    primary_org = models.ForeignKey(Organisations, on_delete=models.SET_NULL, null=True, related_name="root_folders")

    secondary_org = models.ForeignKey(
        Organisations,
        on_delete=models.SET_NULL,
        null=True,
        related_name="secondary_org",
    )
    job_name = models.CharField(max_length=200, blank=False)
    created_date = models.DateTimeField(blank=False)
    pipe_type_sewer = models.BooleanField(null=True, blank=True)
    standard_key = models.ForeignKey(Standard, on_delete=models.SET_NULL, null=True)

    def allows_uploads_from(self, upload_org: Organisations) -> bool:
        """
        :returns: Whether the given organisation is allowed to create files + inspections in this folder
        """

        if upload_org.is_asset_owner:
            if self.secondary_org or self.get_ancestors().exclude(secondary_org=None).exists():
                return False  # Asset owners cannot upload to contractor folders

        if upload_org.is_contractor:
            root_folder = self.get_root()
            if (
                root_folder.primary_org != upload_org
                and self.secondary_org != upload_org
                and not self.get_ancestors().filter(secondary_org=upload_org).exists()
            ):  # Contractors can only upload to their own folders
                return False

        return True

    @property
    def can_contain_inspections(self) -> bool:
        """
        :returns: Whether this folder can contain inspections
        """
        if self.job_name == "Recycle Bin":
            return False
        return not self.is_root() and self.is_leaf()

    @property
    def owning_org(self) -> Organisations:
        """
        :returns: The organisation that this folder belongs to
        """
        root = self.get_root()
        return root.primary_org if root.primary_org else self.primary_org

    @property
    def job_full_path(self):
        ancs = self.get_ancestors().values("job_name")
        job_path = [anc["job_name"] for anc in ancs]
        job_path.append(self.job_name)
        job_full_path = " > ".join(job_path)

        return job_full_path

    @property
    def show_path_and_files(self):
        file_name_list = []
        files = FileList.objects.filter(job_tree=self)

        for f in files:
            file_name_list.append(f.filename)

        return {"path": self.job_full_path, "files": file_name_list}

    def __str__(self):
        return self.job_full_path

    class Meta:
        db_table = "service_jobstree"
