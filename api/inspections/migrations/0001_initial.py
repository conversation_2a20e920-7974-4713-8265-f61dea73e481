# Generated by Django 4.1.2 on 2024-03-14 04:44

from django.conf import settings
import django.contrib.gis.db.models.fields
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import uuid

import api.inspections.models.inspections


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("organisations", "0001_initial"),
        ("defects", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="Asset",
            fields=[
                (
                    "uuid",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "type",
                    models.CharField(
                        choices=[("pipe", "Pipe"), ("manhole", "Manhole")],
                        default="pipe",
                        max_length=7,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "organisation",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="organisations.organisations",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="File",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("filename", models.CharField(blank=True, max_length=200)),
                ("url", models.CharField(blank=True, max_length=200)),
                ("file_size", models.CharField(blank=True, max_length=200)),
                ("file_type", models.CharField(blank=True, max_length=50)),
                ("created_time", models.DateTimeField(auto_now_add=True)),
            ],
            options={
                "db_table": "service_file",
            },
        ),
        migrations.CreateModel(
            name="FileList",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("filename", models.CharField(blank=True, max_length=200)),
                ("url", models.CharField(blank=True, max_length=1000)),
                ("file_size", models.CharField(blank=True, max_length=200)),
                ("file_type", models.CharField(blank=True, max_length=50)),
                ("created_time", models.DateTimeField(auto_now_add=True)),
                ("upload_user", models.CharField(max_length=200)),
                ("total_frames", models.IntegerField(default=0)),
                ("hidden", models.BooleanField(default=False)),
                (
                    "request_endpoint",
                    models.CharField(blank=True, default="", max_length=500),
                ),
                ("play_url", models.CharField(blank=True, max_length=1000)),
                ("upload_completed", models.BooleanField(default=False)),
                ("upload_completed_time", models.DateTimeField(blank=True, null=True)),
                (
                    "processing_started_time",
                    models.DateTimeField(blank=True, null=True),
                ),
                (
                    "processing_completed_time",
                    models.DateTimeField(blank=True, null=True),
                ),
            ],
            options={
                "db_table": "service_filelist",
            },
        ),
        migrations.CreateModel(
            name="ImportedInspectionFile",
            fields=[
                (
                    "uuid",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("blob_storage", models.CharField(max_length=250, null=True)),
                ("file_name", models.CharField(max_length=50, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("imported", models.BooleanField(null=True)),
                (
                    "import_action",
                    models.CharField(
                        choices=[
                            ("ignore_duplicates", "Ignore Duplicates"),
                            ("overwrite_duplicates", "Overwrite Duplicates"),
                            ("create_duplicates", "Create Duplicates"),
                            ("awaiting_action", "Awaiting Action"),
                        ],
                        max_length=25,
                        null=True,
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="Inspection",
            fields=[
                (
                    "uuid",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("legacy_id", models.IntegerField(null=True)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("Planned", "Planned"),
                            ("Uploaded", "Uploaded"),
                            ("Reviewed", "Reviewed"),
                            ("Decision", "Decision"),
                            ("Repair Plan", "Repair Plan"),
                            ("Actioned", "Actioned"),
                            ("Complete", "Complete"),
                            ("Archived", "Archived"),
                        ],
                        max_length=50,
                        null=True,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "asset",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="inspections.asset",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "file",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="inspections.filelist",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="Jobs",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("job_name", models.CharField(max_length=200)),
                ("Created_by", models.CharField(max_length=200)),
                ("Created_date", models.DateTimeField()),
                ("pipe_type_sewer", models.BooleanField(blank=True, null=True)),
                (
                    "Organisation",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="organisations.organisations",
                    ),
                ),
                (
                    "standard_key",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="defects.standard",
                    ),
                ),
            ],
            options={
                "db_table": "service_jobs",
            },
        ),
        migrations.CreateModel(
            name="ResultsFile",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("filename", models.CharField(blank=True, max_length=200)),
                ("file_location", models.CharField(blank=True, max_length=1000)),
                ("file_size", models.CharField(blank=True, max_length=200)),
                ("header_only", models.BooleanField(default=False)),
                (
                    "organisation",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="organisations.organisations",
                    ),
                ),
            ],
            options={
                "db_table": "service_resultsfile",
            },
        ),
        migrations.CreateModel(
            name="ResultsFileAssets",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("video_filename", models.CharField(blank=True, max_length=200)),
                ("asset_id", models.CharField(blank=True, max_length=100, null=True)),
                ("start_node", models.CharField(blank=True, max_length=100, null=True)),
                ("end_node", models.CharField(blank=True, max_length=100, null=True)),
                ("direction", models.CharField(blank=True, max_length=100, null=True)),
                (
                    "chainage",
                    models.CharField(blank=True, default="0", max_length=10, null=True),
                ),
                ("date_captured", models.DateField(blank=True, null=True)),
                (
                    "associated_file",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="inspections.filelist",
                    ),
                ),
                (
                    "results_file",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="inspections.resultsfile",
                    ),
                ),
            ],
            options={
                "db_table": "service_resultsfileassets",
            },
        ),
        migrations.CreateModel(
            name="VideoFrames",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("image_location", models.CharField(blank=True, max_length=200)),
                ("frame_id", models.IntegerField(default=0)),
                (
                    "time_reference",
                    models.CharField(blank=True, default="", max_length=20, null=True),
                ),
                (
                    "chainage",
                    models.CharField(blank=True, default="0", max_length=10, null=True),
                ),
                (
                    "chainage_number",
                    models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
                ),
                ("class_label", models.CharField(blank=True, max_length=200)),
                (
                    "class_certainty",
                    models.DecimalField(blank=True, decimal_places=2, max_digits=5),
                ),
                ("duplicate_of", models.IntegerField(default=-1)),
                ("all_class_breakdown", models.CharField(blank=True, max_length=1000)),
                ("fix_user", models.CharField(blank=True, max_length=200, null=True)),
                (
                    "original_fixed_label",
                    models.CharField(blank=True, max_length=200, null=True),
                ),
                ("is_accepted", models.BooleanField(default=False)),
                ("reviewed_by", models.CharField(max_length=200, null=True)),
                ("review_timestamp", models.DateTimeField(blank=True, null=True)),
                ("is_hidden", models.BooleanField(default=False)),
                ("at_joint", models.BooleanField(blank=True, default=None, null=True)),
                ("at_clock", models.IntegerField(blank=True, default=None, null=True)),
                ("to_clock", models.IntegerField(blank=True, default=None, null=True)),
                ("cont_defect_start", models.BooleanField(default=False)),
                (
                    "cont_defect_end",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        default=None,
                        max_digits=10,
                        null=True,
                    ),
                ),
                (
                    "quantity1_value",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        default=None,
                        max_digits=10,
                        null=True,
                    ),
                ),
                (
                    "quantity1_units",
                    models.CharField(blank=True, default="", max_length=200),
                ),
                (
                    "quantity2_value",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        default=None,
                        max_digits=10,
                        null=True,
                    ),
                ),
                (
                    "quantity2_units",
                    models.CharField(blank=True, default="", max_length=200),
                ),
                (
                    "is_matched",
                    models.BooleanField(blank=True, default=None, null=True),
                ),
                ("remarks", models.TextField(blank=True, null=True)),
                (
                    "defect_model",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="defects.defectmodellist",
                    ),
                ),
                (
                    "defect_scores",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="defects.defectscores",
                    ),
                ),
                (
                    "parent_video",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="inspections.filelist",
                    ),
                ),
            ],
            options={
                "db_table": "service_videoframes",
                "ordering": ["frame_id"],
            },
        ),
        migrations.CreateModel(
            name="ResultsFileDefects",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "time_reference",
                    models.CharField(blank=True, default="", max_length=20, null=True),
                ),
                (
                    "chainage_number",
                    models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
                ),
                ("comment", models.CharField(blank=True, max_length=200)),
                ("defect_code", models.CharField(blank=True, max_length=200)),
                (
                    "associated_video_frame",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="inspections.videoframes",
                    ),
                ),
                (
                    "results_file_asset",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="inspections.resultsfileassets",
                    ),
                ),
            ],
            options={
                "db_table": "service_resultsfiledefects",
            },
        ),
        migrations.CreateModel(
            name="ProcessingList",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("filename", models.CharField(blank=True, max_length=200)),
                ("file_size", models.CharField(blank=True, max_length=200)),
                (
                    "status",
                    models.CharField(blank=True, default="Uploading", max_length=200),
                ),
                ("created_time", models.DateTimeField(auto_now_add=True)),
                ("upload_user", models.CharField(max_length=200)),
                (
                    "request_endpoint",
                    models.CharField(blank=True, default="", max_length=500),
                ),
                ("manual_qa_required", models.BooleanField(default=False)),
                ("sewer_data", models.BooleanField(default=True)),
                ("upload_completed", models.BooleanField(default=False)),
                (
                    "associated_file",
                    models.OneToOneField(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="inspections.filelist",
                    ),
                ),
                (
                    "standard_key",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="defects.standard",
                    ),
                ),
                (
                    "target_org",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="organisations.organisations",
                    ),
                ),
            ],
            options={
                "db_table": "service_processinglist",
            },
        ),
        migrations.CreateModel(
            name="MapPointList",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=200)),
                (
                    "geometry",
                    django.contrib.gis.db.models.fields.PointField(blank=True, null=True, srid=4326),
                ),
                (
                    "chainage",
                    models.FloatField(
                        blank=True,
                        default=0.0,
                        null=True,
                        validators=[
                            django.core.validators.MinValueValidator(0.0),
                            django.core.validators.MaxValueValidator(9999),
                        ],
                    ),
                ),
                (
                    "chainage_unit",
                    models.CharField(blank=True, default="m", max_length=10, null=True),
                ),
                (
                    "diameter",
                    models.CharField(blank=True, default="0", max_length=10, null=True),
                ),
                (
                    "condition_rating",
                    models.IntegerField(
                        choices=[
                            (1, 1),
                            (2, 2),
                            (3, 3),
                            (4, 4),
                            (5, 5),
                            (6, 6),
                            (7, 7),
                            (8, 8),
                            (9, 9),
                        ],
                        null=True,
                    ),
                ),
                (
                    "service_condition_rating",
                    models.IntegerField(choices=[(1, 1), (2, 2), (3, 3), (4, 4)], null=True),
                ),
                ("date_captured", models.DateField(blank=True, null=True)),
                ("asset_id", models.CharField(blank=True, max_length=100, null=True)),
                (
                    "first_frame",
                    models.CharField(blank=True, max_length=1000, null=True),
                ),
                ("material", models.CharField(blank=True, max_length=100, null=True)),
                ("start_node", models.CharField(blank=True, max_length=100, null=True)),
                ("end_node", models.CharField(blank=True, max_length=100, null=True)),
                (
                    "direction",
                    models.CharField(
                        choices=[
                            ("Downstream", "Downstream"),
                            ("Upstream", "Upstream"),
                            ("Unknown", "Unknown"),
                        ],
                        max_length=100,
                        null=True,
                    ),
                ),
                (
                    "upstream_node",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
                (
                    "downstream_node",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
                (
                    "process_model_id",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
                (
                    "cr_model_id",
                    models.CharField(blank=True, max_length=1000, null=True),
                ),
                (
                    "ser_cr_model_id",
                    models.CharField(blank=True, max_length=1000, null=True),
                ),
                ("pipe_type", models.CharField(default="SS", max_length=10)),
                ("is_accepted", models.BooleanField(default=False)),
                ("reviewed_by", models.CharField(max_length=200, null=True)),
                ("review_timestamp", models.DateTimeField(blank=True, null=True)),
                ("water_level_warning", models.BooleanField(default=False)),
                ("loss_of_vision_warning", models.BooleanField(default=False)),
                (
                    "water_level_url",
                    models.CharField(blank=True, default="", max_length=200),
                ),
                ("sewer_data", models.BooleanField(default=True, null=True)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("Planned", "Planned"),
                            ("Uploaded", "Uploaded"),
                            ("Reviewed", "Reviewed"),
                            ("Decision", "Decision"),
                            ("Repair Plan", "Repair Plan"),
                            ("Actioned", "Actioned"),
                            ("Complete", "Complete"),
                            ("Archived", "Archived"),
                        ],
                        default="Uploaded",
                        max_length=20,
                    ),
                ),
                (
                    "inspection_notes",
                    models.CharField(blank=True, max_length=1000, null=True),
                ),
                (
                    "ocr_error_detect",
                    models.FloatField(blank=True, default=0.0, max_length=10, null=True),
                ),
                (
                    "ocr_error_recog",
                    models.FloatField(blank=True, default=0.0, max_length=10, null=True),
                ),
                ("ocr_used_azure", models.BooleanField(default=True, null=True)),
                ("orig_str_grade", models.IntegerField(blank=True, null=True)),
                ("orig_ser_grade", models.IntegerField(blank=True, null=True)),
                ("repair_completed_date", models.DateField(blank=True, null=True)),
                ("deleted_at", models.DateTimeField(blank=True, null=True)),
                (
                    "associated_file",
                    models.OneToOneField(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="inspections.filelist",
                    ),
                ),
                (
                    "inspection",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="inspections.inspection",
                    ),
                ),
                (
                    "job",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="inspections.jobs",
                    ),
                ),
                (
                    "standard_key",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="defects.standard",
                    ),
                ),
            ],
            options={
                "verbose_name_plural": "points",
                "db_table": "service_mappointlist",
            },
        ),
        migrations.CreateModel(
            name="JobsTree",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("path", models.CharField(max_length=255, unique=True)),
                ("depth", models.PositiveIntegerField()),
                ("numchild", models.PositiveIntegerField(default=0)),
                ("job_name", models.CharField(max_length=200)),
                ("created_date", models.DateTimeField()),
                ("pipe_type_sewer", models.BooleanField(blank=True, null=True)),
                (
                    "primary_org",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="primary_org",
                        to="organisations.organisations",
                    ),
                ),
                (
                    "secondary_org",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="secondary_org",
                        to="organisations.organisations",
                    ),
                ),
                (
                    "standard_key",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="defects.standard",
                    ),
                ),
            ],
            options={
                "db_table": "service_jobstree",
            },
        ),
        migrations.CreateModel(
            name="InspectionValue",
            fields=[
                (
                    "uuid",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("value", models.CharField(max_length=255)),
                ("original_value", models.CharField(max_length=255, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "frame",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="inspections.videoframes",
                    ),
                ),
                (
                    "imported_inspection_file",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="inspections.importedinspectionfile",
                    ),
                ),
                (
                    "inspection",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="inspections.inspection",
                    ),
                ),
                (
                    "standard_header",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="defects.standardheader",
                    ),
                ),
            ],
        ),
        migrations.AddField(
            model_name="inspection",
            name="folder",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                to="inspections.jobstree",
            ),
        ),
        migrations.AddField(
            model_name="importedinspectionfile",
            name="folder",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                to="inspections.jobstree",
            ),
        ),
        migrations.AddField(
            model_name="importedinspectionfile",
            name="organisation",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                to="organisations.organisations",
            ),
        ),
        migrations.AddField(
            model_name="filelist",
            name="job",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                to="inspections.jobs",
            ),
        ),
        migrations.AddField(
            model_name="filelist",
            name="job_tree",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="jt",
                to="inspections.jobstree",
            ),
        ),
        migrations.AddField(
            model_name="filelist",
            name="results_file",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                to="inspections.resultsfile",
            ),
        ),
        migrations.AddField(
            model_name="filelist",
            name="target_org",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="target_org",
                to="organisations.organisations",
            ),
        ),
        migrations.AddField(
            model_name="filelist",
            name="upload_org",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="upload_org",
                to="organisations.organisations",
            ),
        ),
        migrations.AddField(
            model_name="filelist",
            name="uploaded_by",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.CreateModel(
            name="ConditionRatingChanges",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "original_condition_rating",
                    models.IntegerField(
                        choices=[
                            (1, 1),
                            (2, 2),
                            (3, 3),
                            (4, 4),
                            (5, 5),
                            (6, 6),
                            (7, 7),
                            (8, 8),
                            (9, 9),
                        ],
                        null=True,
                    ),
                ),
                (
                    "new_condition_rating",
                    models.IntegerField(
                        choices=[
                            (1, 1),
                            (2, 2),
                            (3, 3),
                            (4, 4),
                            (5, 5),
                            (6, 6),
                            (7, 7),
                            (8, 8),
                            (9, 9),
                        ],
                        null=True,
                    ),
                ),
                ("fix_user", models.CharField(default="", max_length=200)),
                ("fix_time", models.DateTimeField(auto_now_add=True)),
                (
                    "associated_file",
                    models.OneToOneField(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="inspections.filelist",
                    ),
                ),
            ],
            options={
                "db_table": "service_conditionratingchanges",
            },
        ),
        migrations.CreateModel(
            name="AssetValue",
            fields=[
                (
                    "uuid",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("value", models.CharField(max_length=255)),
                ("original_value", models.CharField(max_length=255, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "asset",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="inspections.asset",
                    ),
                ),
                (
                    "imported_inspection_file",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="inspections.importedinspectionfile",
                    ),
                ),
                (
                    "standard_header",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="defects.standardheader",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="InspectionFilter",
            fields=[
                (
                    "uuid",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "filter_model",
                    models.JSONField(default=api.inspections.models.inspections.default_filter_model),
                ),
                (
                    "folder_filter_model",
                    models.JSONField(default=api.inspections.models.inspections.default_folder_filter_model),
                ),
                ("last_modified", models.DateTimeField(auto_now=True)),
                (
                    "organisation",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="organisations.organisations",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="inspection_filter",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "unique_together": {("user", "organisation")},
            },
        ),
    ]
