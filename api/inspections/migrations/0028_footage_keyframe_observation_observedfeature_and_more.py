# Generated by Django 4.1.2 on 2025-06-17 05:27

import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        (
            "defects",
            "0035_remove_subfeature_subfeature_numeric_kind_allowed_vals_and_more",
        ),
        ("inspections", "0027_remove_conditionratingchanges_associated_file_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="Footage",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "chainage_unit",
                    models.CharField(
                        choices=[("M", "Metres"), ("FT", "Feet")],
                        default="M",
                        max_length=2,
                    ),
                ),
                ("total_frames", models.PositiveIntegerField(default=0)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "video_file",
                    models.ForeignKey(
                        default=None,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="derived_footage",
                        to="inspections.filelist",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="Keyframe",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("time_reference_milliseconds", models.PositiveIntegerField()),
                ("sequence_number", models.PositiveIntegerField()),
                (
                    "chainage",
                    models.FloatField(
                        default=0.0,
                        validators=[
                            django.core.validators.MinValueValidator(0.0),
                            django.core.validators.MaxValueValidator(9999),
                        ],
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("at_joint", models.BooleanField(default=False)),
                ("has_loss_of_vision", models.BooleanField(default=False)),
                ("has_textbox", models.BooleanField(default=False)),
                ("has_title", models.BooleanField(default=False)),
                (
                    "footage",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="keyframes",
                        to="inspections.footage",
                    ),
                ),
                (
                    "video_frame",
                    models.ForeignKey(
                        default=None,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="keyframes",
                        to="inspections.videoframes",
                    ),
                ),
            ],
            options={
                "ordering": ["footage", "sequence_number"],
            },
        ),
        migrations.CreateModel(
            name="Observation",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("remarks", models.TextField(blank=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "inspection",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="observations",
                        to="inspections.inspection",
                    ),
                ),
                (
                    "keyframes",
                    models.ManyToManyField(
                        related_name="observations", to="inspections.keyframe"
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="ObservedFeature",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "clock_position_from",
                    models.IntegerField(
                        default=None,
                        null=True,
                        validators=[
                            django.core.validators.MinValueValidator(1),
                            django.core.validators.MaxValueValidator(12),
                        ],
                    ),
                ),
                (
                    "clock_position_to",
                    models.IntegerField(
                        default=None,
                        null=True,
                        validators=[
                            django.core.validators.MinValueValidator(1),
                            django.core.validators.MaxValueValidator(12),
                        ],
                    ),
                ),
                (
                    "feature_class_prediction_certainty",
                    models.FloatField(
                        default=None,
                        null=True,
                        validators=[
                            django.core.validators.MinValueValidator(0.0),
                            django.core.validators.MaxValueValidator(1.0),
                        ],
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "code",
                    models.ForeignKey(
                        default=None,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="applications",
                        to="defects.code",
                    ),
                ),
                (
                    "feature",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="observed_instances",
                        to="defects.feature",
                    ),
                ),
                (
                    "observation",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="features",
                        to="inspections.observation",
                    ),
                ),
                (
                    "scoring",
                    models.ForeignKey(
                        default=None,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="applications",
                        to="defects.codescore",
                    ),
                ),
            ],
        ),
        migrations.AlterField(
            model_name="processinglist",
            name="status_reason",
            field=models.CharField(
                blank=True,
                choices=[
                    ("GE", "GENERIC_ERROR"),
                    ("FC", "FILE_CORRUPTED"),
                    ("TW", "TIMEOUT_WHILE_WAITING"),
                    ("TP", "TIMEOUT_WHILE_PROCESSING"),
                ],
                default=None,
                max_length=2,
                null=True,
            ),
        ),
        migrations.CreateModel(
            name="ObservedSubFeature",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("numeric_value", models.FloatField(default=None, null=True)),
                ("numeric_range_min", models.FloatField(default=None, null=True)),
                ("numeric_range_max", models.FloatField(default=None, null=True)),
                (
                    "numeric_unit",
                    models.CharField(
                        choices=[
                            ("PERC", "Percentage"),
                            ("METRE", "Metres"),
                            ("MM", "Millimetres"),
                            ("FEET", "Feet"),
                            ("INCH", "Inches"),
                            ("COUNT", "Count"),
                            ("CUBIC_M", "Cubic Metres"),
                            ("CUBIC_FT", "Cubic Feet"),
                            ("DEGREES", "Degrees"),
                        ],
                        default=None,
                        max_length=10,
                        null=True,
                    ),
                ),
                (
                    "sub_feature_class_prediction_certainty",
                    models.FloatField(
                        default=None,
                        null=True,
                        validators=[
                            django.core.validators.MinValueValidator(0.0),
                            django.core.validators.MaxValueValidator(1.0),
                        ],
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "observed_feature",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="sub_features",
                        to="inspections.observedfeature",
                    ),
                ),
                (
                    "selected_option",
                    models.ForeignKey(
                        default=None,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="observed_instances",
                        to="defects.subfeatureoption",
                    ),
                ),
                (
                    "sub_feature",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="observed_instances",
                        to="defects.subfeature",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="KeyframeObservation",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "keyframe",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="keyframe_observations",
                        to="inspections.keyframe",
                    ),
                ),
                (
                    "observation",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="keyframe_observations",
                        to="inspections.observation",
                    ),
                ),
            ],
        ),
        migrations.AddField(
            model_name="inspection",
            name="footage",
            field=models.ForeignKey(
                default=None,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="inspections",
                to="inspections.footage",
            ),
        ),
        migrations.AddConstraint(
            model_name="observedsubfeature",
            constraint=models.CheckConstraint(
                check=models.Q(
                    models.Q(
                        models.Q(
                            ("numeric_unit__isnull", True),
                            ("numeric_value__isnull", False),
                        ),
                        models.Q(
                            ("numeric_range_min__isnull", False),
                            ("numeric_unit__isnull", True),
                        ),
                        models.Q(
                            ("numeric_range_max__isnull", False),
                            ("numeric_unit__isnull", True),
                        ),
                        _connector="OR",
                    ),
                    _negated=True,
                ),
                name="observed_sub_feature_unit_required_if_numeric_value_or_range_set",
            ),
        ),
        migrations.AddConstraint(
            model_name="observedsubfeature",
            constraint=models.CheckConstraint(
                check=models.Q(
                    models.Q(
                        models.Q(
                            ("numeric_value__isnull", False),
                            ("selected_option__isnull", False),
                        ),
                        models.Q(
                            ("numeric_range_min__isnull", False),
                            ("selected_option__isnull", False),
                        ),
                        models.Q(
                            ("numeric_range_max__isnull", False),
                            ("selected_option__isnull", False),
                        ),
                        _connector="OR",
                    ),
                    _negated=True,
                ),
                name="observed_sub_feature_cannot_have_numeric_value_or_range_if_option_selected",
            ),
        ),
        migrations.AddConstraint(
            model_name="observedsubfeature",
            constraint=models.CheckConstraint(
                check=models.Q(
                    ("numeric_value__isnull", False),
                    ("selected_option__isnull", False),
                    ("numeric_range_min__isnull", False),
                    ("numeric_range_max__isnull", False),
                    _connector="OR",
                ),
                name="observed_sub_feature_requires_numeric_or_categorical_value",
            ),
        ),
        migrations.AddConstraint(
            model_name="keyframe",
            constraint=models.UniqueConstraint(
                fields=("footage", "sequence_number"),
                name="footage_keyframe_unique_sequence_number",
            ),
        ),
        migrations.AddConstraint(
            model_name="footage",
            constraint=models.CheckConstraint(
                check=models.Q(("chainage_unit__in", ["M", "FT"])),
                name="footage_allowed_chainage_units",
            ),
        ),
    ]
