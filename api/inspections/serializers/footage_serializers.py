from rest_framework import serializers
from rest_framework.generics import get_object_or_404

from api.inspections.models import Footage, FileList, Keyframe


class KeyframeSerializer(serializers.ModelSerializer):
    video_frame_id = serializers.IntegerField(required=False, allow_null=True)

    def validate(self, attrs):
        attrs = super().validate(attrs)
        footage = self.context["footage"]
        attrs["footage_id"] = footage.id
        if video_frame_id := attrs.get("video_frame_id"):
            # Note: We iterate over the cached frames queryset like this to avoid hitting the database multiple times.
            if not footage.video_file or not any(
                frame.id == video_frame_id for frame in footage.video_file.videoframes_set.all()
            ):
                raise serializers.ValidationError(
                    "The provided video frame ID does not exist for the footage's video file."
                )

        return attrs

    def create(self, validated_data):
        footage = self.context["footage"]
        keyframe = footage.append_new_keyframe(
            video_frame=validated_data.get("video_frame_id"),
            time_reference_milliseconds=validated_data["time_reference_milliseconds"],
            chainage=validated_data.get("chainage", 0.0),
            is_hidden=validated_data.get("is_hidden", False),
            at_joint=validated_data.get("at_joint", False),
            has_loss_of_vision=validated_data.get("has_loss_of_vision", False),
            has_textbox=validated_data.get("has_textbox", False),
            has_title=validated_data.get("has_title", False),
        )
        return keyframe

    class Meta:
        model = Keyframe
        read_only_fields = [
            "id",
            "created_at",
            "updated_at",
            "footage_id",
            "sequence_number",
        ]
        fields = [
            "id",
            "footage_id",
            "video_frame_id",
            "time_reference_milliseconds",
            "chainage",
            "is_hidden",
            "created_at",
            "updated_at",
            "at_joint",
            "has_loss_of_vision",
            "has_textbox",
            "has_title",
        ]


class FootageSerializer(serializers.ModelSerializer):
    video_file_id = serializers.IntegerField(required=False, allow_null=True)

    def create(self, validated_data):
        org = self.context["request"].organisation
        if video_file_id := validated_data.pop("video_file_id"):
            video_file = get_object_or_404(FileList, id=video_file_id, target_org=org)
        else:
            video_file = None
        return Footage.objects.create_for_org(org, file=video_file)

    class Meta:
        model = Footage
        read_only_fields = [
            "id",
            "total_frames",
            "created_at",
            "updated_at",
        ]
        fields = [
            "id",
            "video_file_id",
            "total_frames",
            "created_at",
            "updated_at",
        ]


class FootageDetailSerializer(serializers.ModelSerializer):
    """
    Detailed representation of inspection footage, including keyframes.
    """

    keyframes = KeyframeSerializer(many=True, read_only=True)

    class Meta:
        model = Footage
        read_only_fields = [
            "id",
            "total_frames",
            "created_at",
            "updated_at",
            "keyframes",
        ]
        fields = [
            "id",
            "video_file_id",
            "total_frames",
            "created_at",
            "updated_at",
            "keyframes",
        ]
