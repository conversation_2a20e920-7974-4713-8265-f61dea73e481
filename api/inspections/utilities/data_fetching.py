from typing import Iterable, TypeVar

from django.conf import settings
from django.core.cache import caches
from django.db.models import QuerySet
from django.http import HttpRequest
from yarl import URL

from api.inspections.models import Inspection, Asset
from api.inspections.pydantic_models.asset_model import AssetModel
from api.inspections.pydantic_models.inspection_model import (
    InspectionModel,
    get_inspection_representation,
)
from api.inspections.serializers.asset_serializers import AssetSerializer

ASSET_CACHE = caches["assets"]
INSPECTION_CACHE = caches["inspections"]


def get_asset_list(queryset: QuerySet[Asset]) -> list[AssetModel]:
    """
    Given a queryset of assets, return a list of AssetModel objects, loading from cache if available.
    Assets are returned respecting the ordering of the queryset.

    :param queryset: The queryset of assets to load
    :return: A list of AssetModel objects
    """

    all_asset_ids_to_idx = {str(uuid): idx for idx, uuid in enumerate(queryset.values_list("uuid", flat=True))}
    raw_cached_assets = ASSET_CACHE.get_many(all_asset_ids_to_idx.keys())
    cached_assets = [AssetModel.model_validate_json(v) for v in raw_cached_assets.values()]
    uncached_asset_ids = set(all_asset_ids_to_idx.keys()).difference(raw_cached_assets.keys())

    uncached_assets_qs = Asset.objects.filter(uuid__in=uncached_asset_ids).prefetch_related(
        "assetvalue_set",
        "assetvalue_set__standard_header",
        "assetvalue_set__standard_header__header",
    )

    uncached_assets: list[AssetModel] = AssetSerializer(
        uncached_assets_qs, many=True, context={"skip_cache": True}
    ).data

    assets_to_cache = {
        str(asset.uuid): asset.model_dump_json(
            exclude_none=True,
            exclude_defaults=True,
            exclude_unset=True,
            exclude=AssetModel.model_computed_fields,
        )
        for asset in uncached_assets
    }
    ASSET_CACHE.set_many(assets_to_cache, timeout=settings.DEFAULT_ENTITY_CACHE_TIMEOUT)

    all_assets_list = cached_assets + uncached_assets

    # Restore the original queryset ordering
    all_assets_list.sort(key=lambda asset: all_asset_ids_to_idx[str(asset.uuid)])
    return all_assets_list


def get_inspection_list(queryset: Iterable[Inspection]) -> list[InspectionModel]:
    """
    Build an inspection list from a queryset of records.

    Inspections and assets are both preloaded from the cache, if available, else loaded from the database and cached.
    Inspections are returned respecting the order of the queryset

    :param queryset: A queryset of Inspection records.
    :return: A list of Inspection models.
    """

    assets_qs = Asset.objects.filter(uuid__in={str(i.asset_id) for i in queryset})
    all_assets_by_id = {str(asset.uuid): asset for asset in get_asset_list(assets_qs)}

    # Preload all inspections, first from cache, then from the database
    inspection_ids_to_idx = {str(insp.uuid): idx for idx, insp in enumerate(queryset)}
    all_inspection_ids = set(inspection_ids_to_idx.keys())
    raw_cached_inspections = INSPECTION_CACHE.get_many(all_inspection_ids)
    cached_inspections_by_id = {k: InspectionModel.model_validate_json(v) for k, v in raw_cached_inspections.items()}
    for insp in cached_inspections_by_id.values():  # Link preloaded inspections to their assets
        insp.asset = all_assets_by_id.get(str(insp.asset.uuid))

    uncached_inspection_ids = all_inspection_ids.difference(cached_inspections_by_id)
    uncached_inspections_qs = (
        Inspection.objects.filter(uuid__in=uncached_inspection_ids)
        .prefetch_related(
            "inspectionvalue_set",
            "inspectionvalue_set__standard_header",
            "inspectionvalue_set__standard_header__header",
        )
        .select_related("file", "file__upload_org", "file__job_tree", "folder", "folder__standard_key")
    )

    uncached_inspections_by_id = {
        str(insp.uuid): get_inspection_representation(
            insp, asset=all_assets_by_id.get(str(insp.asset_id)), skip_cache=True
        )
        for insp in uncached_inspections_qs
    }
    inspections_to_cache = {
        str(insp.inspection_id): insp.model_dump_json(
            exclude_none=True,
            exclude_defaults=True,
            exclude_unset=True,
            exclude=InspectionModel.get_cache_excluded_fields(),
        )
        for insp in uncached_inspections_by_id.values()
    }
    INSPECTION_CACHE.set_many(inspections_to_cache, timeout=settings.DEFAULT_ENTITY_CACHE_TIMEOUT)

    all_inspections_by_id = cached_inspections_by_id | uncached_inspections_by_id
    sorted_inspections = sorted(
        all_inspections_by_id.values(), key=lambda insp: inspection_ids_to_idx[str(insp.inspection_id)]
    )
    return sorted_inspections


T = TypeVar("T")


def paginate_queryset(queryset: QuerySet[T], page: int, page_size: int) -> QuerySet[T]:
    """
    Apply offset pagination to a queryset.

    :param queryset: The queryset to paginate.
    :param page: The page number to retrieve (starting from 1).
    :param page_size: The number of records per page.
    :return: A paginated queryset.
    """
    offset = (page - 1) * page_size
    limit = offset + page_size
    return queryset[offset:limit]


def build_next_link(request: HttpRequest, *, count: int, current_page: int, page_size: int) -> str | None:
    """
    Build a next link for a paginated response.

    :param request: The request object.
    :param count: The total number of records in the queryset.
    :param current_page: The current page number.
    :param page_size: The number of records per page.
    :return: A URL string for the next page, or None if there is no next page.
    """
    if count > current_page * page_size:
        url = URL(request.build_absolute_uri()).update_query({"page": current_page + 1, "page_size": page_size})
        return str(url)
    return None


def build_prev_link(request: HttpRequest, *, current_page: int, page_size: int) -> str | None:
    """
    Build a previous link for a paginated response.

    :param request: The request object.
    :param current_page: The current page number.
    :param page_size: The number of records per page.
    :return: A URL string for the previous page, or None if there is no previous page.
    """
    if current_page > 1:
        url = URL(request.build_absolute_uri()).update_query({"page": current_page - 1, "page_size": page_size})
        return str(url)
    return None


def build_paginated_response_payload(
    request: HttpRequest,
    results: list,
    *,
    count: int,
    current_page: int,
    page_size: int,
) -> dict:
    """
    Build a response payload to be passed to a renderer for a paginated response.
    :param request: The request object
    :param results: The list of items in the current page
    :param count: The total number of records in the queryset
    :param current_page: The current page number (starting from 1)
    :param page_size: The number of records per page
    :return: A dictionary containing the paginated response payload
    """
    prev_link = build_prev_link(request, current_page=current_page, page_size=page_size)
    next_link = build_next_link(request, count=count, current_page=current_page, page_size=page_size)
    return {
        "results": results,
        "count": count,
        "previous": prev_link,
        "next": next_link,
    }
