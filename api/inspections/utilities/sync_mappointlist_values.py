from datetime import datetime

from dateutil.parser import parse
from django.core.exceptions import ValidationError

from api.inspections.pydantic_models.inspection_model import InspectionModel
from api.inspections.models import MapPointList
from vapar.constants.pipes import DirectionEnum


def convert_date_captured(update_value: str) -> datetime.date:
    """
    Convert inspection 'date captured' to MappointList format.
    """
    try:
        return parse(str(update_value)).date()
    except ValueError:
        raise ValidationError("Invalid date format")


def convert_opposite_of_direction(update_value: DirectionEnum) -> DirectionEnum:
    """
    Convert inspection 'opposite of direction' to MappointList format.
    """
    if update_value == DirectionEnum.UPSTREAM:
        opposite_of_direction = DirectionEnum.DOWNSTREAM
    elif update_value == DirectionEnum.DOWNSTREAM:
        opposite_of_direction = DirectionEnum.UPSTREAM
    else:
        opposite_of_direction = DirectionEnum.UNKNOWN

    return opposite_of_direction


def convert_direction(update_value: str) -> str:
    """
    Convert inspection direction to the MappointList format.
    """
    if update_value not in list(DirectionEnum):
        if update_value == "U":
            direction = DirectionEnum.UPSTREAM.value
        elif update_value == "D":
            direction = DirectionEnum.DOWNSTREAM.value
        else:
            direction = DirectionEnum.UNKNOWN
    else:
        direction = update_value

    return direction


def convert_review_timestamp(update_value: str, date_captured: datetime):
    """
    Convert inspection 'review timestamp' to the Mappointlist format.
    """
    try:
        time_format = "%Y-%m-%d %I:%M%p"
        adjusted_time_str = datetime.strptime(update_value, time_format).strftime("%H:%M")
        date_string = parse(str(date_captured)).date()
        datetime_str = f"{date_string} {adjusted_time_str}"
        review_timestamp = datetime.strptime(datetime_str, "%Y-%m-%d %H:%M")
    except Exception:
        review_timestamp = None
    return review_timestamp


def sync_to_mappointlist(mappointlist: MapPointList, inspection: InspectionModel, mapped_fields: list[str]) -> None:
    """
    Takes an inspection and updates a mappointlist record, ensuring the values are in the correct format.

    :param mappointlist: The MapPointList object to update.
    :param inspection: The Inspection model containing the updates values.
    :param mapped_fields: The list of fields to copy across.
    """
    serialized_inspection = inspection.model_dump(include=set(mapped_fields))
    # perform value conversion needed for MPL data
    if "date_captured" in mapped_fields and serialized_inspection["date_captured"]:
        serialized_inspection["date_captured"] = convert_date_captured(serialized_inspection["date_captured"])
    if "direction" in mapped_fields:
        serialized_inspection["direction"] = convert_direction(serialized_inspection["direction"])
    if "review_timestamp" in mapped_fields:
        serialized_inspection["review_timestamp"] = convert_review_timestamp(
            update_value=serialized_inspection["review_timestamp"],
            date_captured=serialized_inspection["date_captured"],
        )

    for key, value in serialized_inspection.items():
        setattr(mappointlist, key, value)
    mappointlist.set_start_end_node()
    mappointlist.save()
