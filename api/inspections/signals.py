"""
Inspections app signals.
"""

from django.db.models import signals
from django.dispatch import receiver
from django.utils import timezone

from api.inspections.models import InspectionValue, Inspection, Asset, AssetValue, FileList, VideoFrames
from api.recommendations.models import RepairRecommendation, Custom_Repair_Values


@receiver(signals.post_save, sender=Inspection)
def inspection_post_create(sender, instance, created, *args, **kwargs):
    if not created:
        return

    if instance.file and not instance.legacy_id:
        # being created with footage, not imported from csv
        instance.legacy_id = instance.generate_vapar_id()
        instance.save()

    # TODO: logic to create a vapar id during csv import - TBD.


@receiver([signals.post_save, signals.pre_delete], sender=InspectionValue)
def update_inspection_timestamp_on_inspection_value_update(sender, instance: InspectionValue, *args, **kwargs):
    Inspection.objects.filter(uuid=instance.inspection_id).update(last_related_update=timezone.now())


@receiver(signals.post_save, sender=Asset)
def update_inspection_timestamp_on_asset_update(sender, instance: Asset, *args, **kwargs):
    Inspection.objects.filter(asset=instance).update(last_related_update=timezone.now())


@receiver([signals.post_save, signals.pre_delete], sender=AssetValue)
def update_inspection_timestamp_on_asset_value_update(sender, instance: AssetValue, *args, **kwargs):
    Inspection.objects.filter(asset=instance.asset_id).update(last_related_update=timezone.now())


@receiver(signals.post_save, sender=FileList)
def update_inspection_timestamp_on_video_update(sender, instance: FileList, *args, **kwargs):
    Inspection.objects.filter(file=instance).update(last_related_update=timezone.now())


@receiver([signals.post_save, signals.pre_delete], sender=VideoFrames)
def update_inspection_timestamp_on_video_frame_update(sender, instance: VideoFrames, *args, **kwargs):
    Inspection.objects.filter(file_id=instance.parent_video_id).update(last_related_update=timezone.now())


@receiver([signals.post_save, signals.pre_delete], sender=RepairRecommendation)
def update_inspection_timestamp_on_repair_recommendation_update(
    sender, instance: RepairRecommendation, *args, **kwargs
):
    Inspection.objects.filter(mappointlist=instance.target_id).update(last_related_update=timezone.now())


@receiver([signals.post_save, signals.pre_delete], sender=Custom_Repair_Values)
def update_inspection_timestamp_on_custom_repair_values_update(sender, instance: Custom_Repair_Values, *args, **kwargs):
    Inspection.objects.filter(mappointlist=instance.point).update(last_related_update=timezone.now())
