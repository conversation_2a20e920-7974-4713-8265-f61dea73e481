from django.db.transaction import atomic
from djangorestframework_camel_case.parser import CamelCaseJSONParser
from djangorestframework_camel_case.render import Camel<PERSON>aseJ<PERSON><PERSON>enderer
from drf_spectacular.utils import extend_schema
from rest_framework import status
from rest_framework.generics import CreateAPIView, RetrieveAPIView, GenericAPIView
from rest_framework.response import Response

from api.common.permissions import IsAuthenticated, IsServiceUser
from api.inspections.models import Footage
from api.inspections.permissions import IsOrgOwnerOfFootage
from api.inspections.serializers.footage_serializers import (
    FootageSerializer,
    FootageDetailSerializer,
    KeyframeSerializer,
)


class FootageCreateView(CreateAPIView):
    serializer_class = FootageSerializer
    permission_classes = [IsAuthenticated, IsServiceUser]

    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]

    def post(self, request, *args, **kwargs):
        """
        Create a new footage record with no keyframes
        """
        return super().post(request, *args, **kwargs)


class FootageKeyframesCreateDestroyView(GenericAPIView):
    serializer_class = KeyframeSerializer
    permission_classes = [IsAuthenticated, IsServiceUser]

    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]

    queryset = Footage.objects.all().select_related("video_file").prefetch_related("video_file__videoframes_set")

    lookup_url_kwarg = "footage_id"

    pagination_class = None

    def get_serializer_context(self):
        ctx = super().get_serializer_context()
        ctx["footage"] = self.get_object()
        return ctx

    @extend_schema(
        request=KeyframeSerializer(many=True),
        responses={status.HTTP_201_CREATED: KeyframeSerializer(many=True)},
    )
    @atomic
    def put(self, request, *args, **kwargs):
        """
        Create a sequence of keyframes for given footage. Replaces any existing keyframes.
        """

        footage = self.get_object()
        footage.clear_keyframes()

        serializer = self.get_serializer(data=request.data, many=True)
        serializer.is_valid(raise_exception=True)
        serializer.save(footage_id=footage.id)
        return Response(serializer.data, status=status.HTTP_201_CREATED)

    @atomic
    @extend_schema(
        responses={status.HTTP_204_NO_CONTENT: None},
    )
    def delete(self, request, *args, **kwargs):
        """
        Delete all keyframes for given footage.
        """
        footage = self.get_object()
        footage.clear_keyframes()
        return Response(status=status.HTTP_204_NO_CONTENT)


class FootageDetailView(RetrieveAPIView):
    queryset = Footage.objects.all()
    pagination_class = None

    lookup_url_kwarg = "footage_id"

    serializer_class = FootageDetailSerializer
    permission_classes = [IsAuthenticated, IsOrgOwnerOfFootage]

    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]

    def get_queryset(self):
        org = self.request.organisation
        return super().get_queryset().filter(target_org=org)

    def get(self, request, *args, **kwargs):
        """
        Get footage details, including keyframes
        """
        return super().get(request, *args, **kwargs)
