import logging
from datetime import datetime
from typing import Any

import jsonschema
import pydantic
from django.conf import settings
from django.core.cache import caches
from django.db.models import Q, QuerySet, Exists, OuterRef
from django.db.transaction import atomic
from django.http import Http404
from djangorestframework_camel_case.parser import Camel<PERSON><PERSON><PERSON><PERSON>NParser
from djangorestframework_camel_case.render import Camel<PERSON><PERSON>J<PERSON>NRenderer
from drf_spectacular.types import OpenApiTypes
from drf_spectacular.utils import extend_schema, OpenApiRequest, OpenApiParameter
from rest_framework import status
from rest_framework.exceptions import (
    ParseError,
    PermissionDenied,
    ValidationError,
)
from rest_framework.generics import (
    RetrieveUpdateAPIView,
    UpdateAPIView,
    ListCreateAPIView,
    ListAPIView,
    get_object_or_404,
)
from rest_framework.parsers import JSONParser
from rest_framework.renderers import J<PERSON><PERSON>enderer
from rest_framework.response import Response
from rest_framework.views import APIView

from api.base.models import Header
from api.common.enums import OperatorEnum, QueryFilterDataTypeEnum
from api.common.pagination import StandardResultsSetPagination
from api.common.permissions import (
    HasOrganisationAccess,
    IsStandardUser,
    HasAccessToOrgScopedObject,
    IsProductOwner,
    IsAuthenticated,
    has_organisation_access_asset_owner,
    IsServiceUser,
    IsAssetOwnerOrg,
)
from api.defects.models import StandardHeader, Standard
from api.imports.models import Import, ImportedAsset
from api.inspections import schemas, analytics
from api.inspections.models import Asset, AssetValue, MapPointList, Inspection
from api.inspections.pydantic_models.asset_filter import (
    AssetSearchFilterModel,
    AssetFilterEntityEnum,
    build_asset_value_order_subquery,
    build_asset_order_subquery,
)
from api.inspections.pydantic_models.asset_model import (
    AssetModel,
    AssetPatchModel,
    AssetPostModel,
    AssetMatchOrCreateRequest,
    AssetMatchOrCreateResponse,
)
from api.inspections.pydantic_models.inspection_model import (
    get_inspection_representation,
    InspectionModel,
)
from api.inspections.serializers.asset_serializers import (
    AssetSerializer,
    AssetValueSerializer,
    AssetDeleteSerializer,
)
from api.inspections.utilities.data_fetching import (
    get_inspection_list,
    get_asset_list,
    paginate_queryset,
    build_paginated_response_payload,
)
from api.inspections.utilities.sync_mappointlist_values import sync_to_mappointlist
from api.inspections.utilities.validate_inspections import (
    validate_standard_value,
)
from api.organisations.models import Organisations

log = logging.getLogger(__name__)

ASSET_CACHE = caches["assets"]
INSPECTION_CACHE = caches["inspections"]


def get_cached_assets(asset_uuids: set[str]) -> dict[str, Any]:
    """
    Retrieve any cached assets for a given uuid set.

    :param asset_uuids: Set of asset uuids to check against cached assets.
    :return: Dictionary of cached assets with asset uuid as keys.
    """
    log.debug(f"start 'cache.get_many': {datetime.now()}")
    cached_assets_raw = ASSET_CACHE.get_many(asset_uuids)
    cached_assets = restore_cached_assets(cached_assets_raw)
    log.debug(f"CACHE_HITS: {len(cached_assets)}")
    log.debug(f"CACHE_MISSES: {len(asset_uuids) - len(cached_assets)}")

    return cached_assets


def restore_cached_assets(cached_assets: dict[str, str | bytes]) -> dict[str, AssetModel]:
    """
    Restore Asset data from cached JSON objects.

    :param cached_assets: The dictionary of cached JSON objects, by uuid.
    :return: A dictionary of Asset models, with asset uuids as keys.
    """

    return {k: AssetModel.model_validate_json(i) for k, i in cached_assets.items()}


def clear_from_asset_cache(asset_uuids: list[str]):
    """
    Removes assets and linked inspections from the cache to ensure it refreshes
    """
    ASSET_CACHE.delete_many(asset_uuids)

    inspection_ids = Inspection.objects.filter(asset_id__in=asset_uuids).distinct().values_list("uuid", flat=True)
    INSPECTION_CACHE.delete_many(list(inspection_ids))


class AssetListCreateDestroy(ListCreateAPIView):
    permission_classes = [IsAuthenticated, HasOrganisationAccess, IsAssetOwnerOrg]
    queryset = Asset.objects.all().order_by("-created_at")
    http_method_names = ["get", "post", "delete", "put"]

    serializer_class = AssetSerializer
    search_filter_headers = [
        "AssetID",
        "UpstreamNode",
        "DownstreamNode",
        "Material",
        "LocationStreet",
        "LocationTown",
        "HeightDiameter",
    ]
    pagination_class = StandardResultsSetPagination

    ordering_fields = {
        "uuid": ("uuid", AssetFilterEntityEnum.ASSET, QueryFilterDataTypeEnum.STRING, ""),
        "asset_id": ("AssetID", AssetFilterEntityEnum.ASSET_VALUE, QueryFilterDataTypeEnum.STRING, ""),
        "upstream_node": ("UpstreamNode", AssetFilterEntityEnum.ASSET_VALUE, QueryFilterDataTypeEnum.STRING, ""),
        "downstream_node": ("DownstreamNode", AssetFilterEntityEnum.ASSET_VALUE, QueryFilterDataTypeEnum.STRING, ""),
        "material": ("Material", AssetFilterEntityEnum.ASSET_VALUE, QueryFilterDataTypeEnum.STRING, ""),
        "height_diameter": ("HeightDiameter", AssetFilterEntityEnum.ASSET_VALUE, QueryFilterDataTypeEnum.NUMBER, 0),
        "location_street": ("LocationStreet", AssetFilterEntityEnum.ASSET_VALUE, QueryFilterDataTypeEnum.STRING, ""),
        "location_town": ("LocationTown", AssetFilterEntityEnum.ASSET_VALUE, QueryFilterDataTypeEnum.STRING, ""),
    }

    def get_permissions(self):
        if self.request.method == "DELETE":
            return [permission() for permission in self.permission_classes]
        return [permission() for permission in self.permission_classes if permission != IsAssetOwnerOrg]

    def create_search_filters(self, search_term: str) -> list[AssetSearchFilterModel]:
        return [
            AssetSearchFilterModel(
                name=header_name,
                value=[search_term],
                operator=OperatorEnum.CT,
            )
            for header_name in self.search_filter_headers
        ]

    def get_queryset(self):
        org_id = self.request.query_params.get("organisation_id")

        qs = self.queryset.filter(organisation_id=org_id)
        if standard_id := self.request.query_params.get("standard"):  # Standard is optional
            qs = qs.filter(assetvalue__standard_header__standard_id=standard_id)

        if pipe_type := self.request.query_params.get("pipe_type"):
            qs = qs.filter(assetvalue__standard_header__header__name="UseOfDrainSewer", assetvalue__value=pipe_type)

        if search_term := self.request.query_params.get("search"):
            search_filters = self.create_search_filters(search_term)
            search_query = Q()
            for sf in search_filters:
                search_query |= sf.to_query()
            qs = qs.filter(search_query)

        qs = self.order_queryset(qs)

        return qs.distinct()

    def order_queryset(self, queryset):
        ordering_field = self.request.query_params.get("ordering")
        if not ordering_field:
            return queryset

        is_descending = ordering_field.startswith("-")
        prefix = "-" if is_descending else ""
        field = ordering_field[1:] if is_descending else ordering_field

        if field not in self.ordering_fields:
            return queryset

        header_name, filter_entity, data_type, fallback_value = self.ordering_fields[field]

        if filter_entity == AssetFilterEntityEnum.ASSET:
            ordering_column = build_asset_order_subquery(header_name, data_type, fallback_value)
        else:  # ASSET_VALUE
            ordering_column = build_asset_value_order_subquery(header_name, data_type, fallback_value)

        qs = queryset.annotate(ordering_column=ordering_column)
        return qs.order_by(f"{prefix}ordering_column", "-created_at")

    @extend_schema(
        parameters=[
            OpenApiParameter("organisation_id", type=OpenApiTypes.INT, required=True),
            OpenApiParameter("standard", type=OpenApiTypes.INT),
            OpenApiParameter("search", type=OpenApiTypes.STR),
            OpenApiParameter("pipe_type", type=OpenApiTypes.STR, description="Filter by pipe type - 'SS' or 'SW'"),
        ],
        responses={status.HTTP_200_OK: AssetModel},
    )
    def get(self, request, *args, **kwargs):
        organisation_id = request.query_params.get("organisation_id")

        if not organisation_id:
            raise ValidationError("Must provide an organisation_id")

        try:
            page_number = int(request.query_params.get("page", 1))
            page_size = int(request.query_params.get("page_size", settings.ASSET_LIST_DEFAULT_PAGE_SIZE))
            if page_number < 1 or page_size < 1 or page_size > settings.ASSET_LIST_MAX_PAGE_SIZE:
                raise ValueError()
        except ValueError:
            raise ValidationError("Invalid page or page_size given")

        standard_id = request.query_params.get("standard")
        if standard_id is not None and not Standard.objects.filter(id=standard_id).exists():
            raise ValidationError("Invalid standard id")

        organisation = Organisations.objects.get(id=organisation_id)
        self.check_object_permissions(request, organisation)

        queryset = self.get_queryset()
        asset_count = queryset.count()

        queryset = paginate_queryset(queryset, page_number, page_size)
        asset_list = get_asset_list(queryset)

        assets = [asset.model_dump(by_alias=True) for asset in asset_list]
        payload = build_paginated_response_payload(
            request, assets, count=asset_count, current_page=page_number, page_size=page_size
        )
        return Response(payload)

    @extend_schema(request=OpenApiRequest(AssetPostModel), responses={status.HTTP_201_CREATED: AssetModel})
    @atomic()
    def post(self, request, *args, **kwargs):
        organisation_id = request.data.get("organisation")
        organisation = Organisations.objects.get(id=organisation_id)

        standard_id = request.data.get("standard")
        standard = Standard.objects.filter(id=standard_id).first()

        if not standard:
            standard = organisation.standard_key

        if not organisation:
            raise ValidationError("Organisation not found")

        if not HasOrganisationAccess().has_object_permission(request, view=self, organisation=organisation):
            raise PermissionDenied("You do not have permission to create an Asset for this organisation")

        serializer = self.serializer_class(data=request.data, context={"request": request, "standard": standard})
        serializer.is_valid(raise_exception=True)
        instance = serializer.save()

        asset = AssetSerializer().to_representation(instance)
        analytics.send_asset_created_event(
            asset=asset,
            pipe_type=asset.pipe_type,
            creator=request.user,
            owner_org=instance.organisation,
        )
        asset_dumped = asset.model_dump(by_alias=True)
        return Response(asset_dumped, status=status.HTTP_201_CREATED)

    @extend_schema(
        request=AssetDeleteSerializer,
        responses={status.HTTP_204_NO_CONTENT},
    )
    @atomic()
    def delete(self, request, *args, **kwargs):
        serializer = AssetDeleteSerializer(data=request.data)

        serializer.is_valid(raise_exception=True)
        asset_uuids = serializer.validated_data["asset_uuids"]

        assets = Asset.objects.filter(uuid__in=asset_uuids, organisation=request.organisation)
        if assets.count() != len(asset_uuids):
            raise ValidationError("One or more assets do not exist, or are not accessible by this user")

        for asset in assets:
            if asset.inspection_set.exists():
                raise PermissionDenied(f"Asset {asset.uuid} has linked inspections and cannot be deleted")

        clear_from_asset_cache(asset_uuids=asset_uuids)
        assets.delete()

        return Response(status=status.HTTP_204_NO_CONTENT)

    @extend_schema(
        request={
            "application/json": {
                "schema": {"type": "array", "items": {"$ref": "#/components/schemas/AssetMatchOrCreateRequestRequest"}}
            }
        },
        responses={status.HTTP_200_OK: AssetSerializer(many=True)},
    )
    @atomic()
    def put(self, request):
        """
        Create or update multiple assets with the provided values.
        """
        if request.organisation is None:
            raise ValidationError("Organisation not provided")
        try:
            assets_to_process = [AssetMatchOrCreateRequest.model_validate(asset_data) for asset_data in request.data]
        except pydantic.ValidationError as e:
            raise ValidationError(str(e))

        responses = []
        for asset_data in assets_to_process:
            organisation = request.organisation
            if asset_data.organisation_id:
                organisation = Organisations.objects.get(pk=asset_data.organisation_id)
                if not HasOrganisationAccess().has_object_permission(request, self, organisation=organisation):
                    raise PermissionDenied("Access denied to the specified organisation")
            asset = self._build_matching_query(asset_data).first()
            if asset:
                asset = self._update_existing_asset(asset, asset_data)
            else:
                asset = self._create_new_asset(asset_data, organisation)

            responses.append(AssetSerializer(asset).data)

        return Response(responses, status=status.HTTP_200_OK)

    def _build_matching_query(self, asset_data):
        organisation_id = asset_data.organisation_id or self.request.organisation.id
        header_names_to_values = asset_data.asset_value_fields()

        qs = Asset.objects.filter(organisation_id=organisation_id)
        qs = qs.exclude(
            Exists(
                Inspection.objects.filter(
                    asset=OuterRef("pk"),
                ).filter(~Q(folder__standard_key=asset_data.standard_id))
            )
        )

        if asset_id := header_names_to_values.get("AssetID"):
            return qs.filter(
                assetvalue__standard_header__header__name="AssetID",
                assetvalue__value__iexact=asset_id,
            )

        for field_name, value in header_names_to_values.items():
            if value is None:
                continue
            qs = qs.filter(assetvalue__standard_header__header__name=field_name, assetvalue__value=str(value))

        return qs

    def _create_new_asset(self, asset_data, organisation):
        fields_to_create = {k: v for k, v in asset_data.asset_value_fields().items() if v is not None}
        creation_payload = {
            **fields_to_create,
            "organisation": organisation.id,
            "type": "pipe",
            "standard": asset_data.standard_id,
        }
        serializer = AssetSerializer(
            data=creation_payload, context={
                "request": self.request,
                "standard": asset_data.standard_id,
            }
        )
        serializer.is_valid(raise_exception=True)
        asset = serializer.save()
        if asset_data.imported_from_id:
            import_obj = Import.objects.filter(id=asset_data.imported_from_id).first()
            ImportedAsset.objects.create(asset=asset, import_obj=import_obj)
        return asset

    def _update_existing_asset(self, asset, asset_data):
        fields_to_update = {k: v for k, v in asset_data.asset_value_fields().items() if v is not None}
        for field_name, value in fields_to_update.items():
            setattr(asset, field_name, value)
        asset.save()
        if asset_data.imported_from_id:
            import_obj = Import.objects.filter(id=asset_data.imported_from_id).first()
            ImportedAsset.objects.create(asset=asset, import_obj=import_obj)
        return asset


class AssetRetrieveUpdate(RetrieveUpdateAPIView):
    permission_classes = [IsAuthenticated, HasAccessToOrgScopedObject]
    queryset = Asset.objects.prefetch_related("assetvalue_set__standard_header__header").all()
    lookup_field = "uuid"
    http_method_names = ["get", "patch", "delete"]
    serializer_class = AssetSerializer

    @extend_schema(responses={status.HTTP_200_OK: AssetModel})
    def get(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = self.get_serializer()
        asset = serializer.to_representation(instance)
        return Response(asset.model_dump(by_alias=True), status=status.HTTP_200_OK)

    @extend_schema(request=OpenApiRequest(AssetPatchModel), responses={status.HTTP_200_OK: AssetModel})
    @atomic()
    def patch(self, request, *args, **kwargs):
        if "organisation" in request.data and not IsProductOwner().has_permission(request, self):
            raise PermissionDenied("You cannot change the organisation for this asset")

        response = super().patch(request, *args, **kwargs)
        asset = AssetModel(**response.data)
        asset_dumped = asset.model_dump(by_alias=True)
        mapped_headers = Header.objects.filter(
            ~Q(mapped_mpl_field__in=("", "start_node", "end_node", "opposite of direction")), Q(type="asset")
        ).values_list("mapped_mpl_field", flat=True)

        clear_from_asset_cache(asset_uuids=[asset_dumped["uuid"]])

        mappoints = MapPointList.objects.filter(inspection__asset_id=asset_dumped["uuid"])
        for mappoint in mappoints:
            inspection = get_inspection_representation(mappoint.inspection, asset=asset)
            sync_to_mappointlist(
                mappointlist=mappoint,
                inspection=inspection,
                mapped_fields=mapped_headers,
            )
        instance = self.get_object()
        pipe_type_val = instance.assetvalue_set.filter(standard_header__header__name="UseOfDrainSewer").first()
        pipe_type = pipe_type_val.value if pipe_type_val else ""
        analytics.send_asset_updated_event(
            asset=asset, pipe_type=pipe_type, updater=request.user, owner_org=instance.organisation
        )
        return Response(asset_dumped, status=status.HTTP_200_OK)

    def delete(self, request, *args, **kwargs):
        asset = self.get_object()

        if not IsProductOwner().has_permission(request, self):
            if not request.user.is_asset_owner() and not has_organisation_access_asset_owner(
                request, asset.organisation
            ):
                raise PermissionDenied("You do not have permission to delete this asset")

        linked_inspections = asset.inspection_set.all()

        if linked_inspections:
            raise PermissionDenied("You are unable to delete assets with linked inspections")

        asset.delete()

        clear_from_asset_cache(asset_uuids=[asset.uuid])

        return Response(status=status.HTTP_204_NO_CONTENT)


class AssetInspectionsList(ListAPIView):
    permission_classes = [IsAuthenticated]
    http_method_names = ["get"]
    queryset = Inspection.objects.all()

    def get_queryset(self, *args, **kwargs):
        queryset = super().get_queryset().visible_to_org(self.request.organisation)

        asset = get_object_or_404(Asset, pk=self.kwargs.get("uuid"))
        if not HasAccessToOrgScopedObject().has_object_permission(self.request, view=self, obj=asset):
            raise PermissionDenied("You do not have permission to view this asset")

        return queryset.filter(asset=asset)

    @extend_schema(
        responses={status.HTTP_200_OK: InspectionModel},
    )
    def get(self, request, *args, **kwargs):
        """
        List inspections on the asset
        """
        queryset = self.get_queryset()

        inspection_list = get_inspection_list(queryset=queryset)

        inspections = [inspection.model_dump(by_alias=True) for inspection in inspection_list]

        page = self.paginate_queryset(inspections)

        if page is not None:
            paginated_response = self.get_paginated_response(page)
            return paginated_response

        return Response(page, status=status.HTTP_200_OK)


class AssetValueDetail(UpdateAPIView):
    permission_classes = [IsAuthenticated, IsStandardUser, HasOrganisationAccess]
    serializer_class = AssetValueSerializer
    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]
    request_schema = schemas.update_value_request_schema
    http_method_names = ["patch"]

    def get_object(self, uuid):
        try:
            return AssetValue.objects.get(uuid=uuid)
        except AssetValue.DoesNotExist:
            raise Http404

    def check_for_mapped_field(self, asset: Asset, standard_header: StandardHeader):
        mapped_mpl_field = standard_header.get_mapped_mpl_field()

        if mapped_mpl_field:
            related_mappointlists = MapPointList.objects.filter(inspection__asset=asset.uuid)
            for mappointlist in related_mappointlists:
                inspection_model = get_inspection_representation(inspection=mappointlist.inspection, skip_cache=True)
                sync_to_mappointlist(
                    mappointlist=mappointlist,
                    inspection=inspection_model,
                    mapped_fields=[mapped_mpl_field],
                )

    @extend_schema(
        request=OpenApiRequest(request_schema),
        responses={status.HTTP_200_OK: AssetValueSerializer()},
    )
    def patch(self, request, uuid):
        """
        Update an asset value record.
        """
        try:
            jsonschema.validate(instance=request.data, schema=self.request_schema)
        except jsonschema.ValidationError as error:
            raise ParseError(error.message)

        asset_value = self.get_object(uuid)
        self.check_object_permissions(request, asset_value.asset.organisation)

        data = request.data.copy()
        data["standard_header"] = asset_value.standard_header.uuid
        data["asset"] = asset_value.asset.uuid

        serializer = self.serializer_class(data=data, instance=asset_value)
        serializer.is_valid(raise_exception=True)
        instance = AssetValue(**serializer.validated_data)
        validation_errors = validate_standard_value(instance)

        if len(validation_errors) > 0:
            raise ValidationError(validation_errors)

        serializer.save()

        self.check_for_mapped_field(asset=asset_value.asset, standard_header=asset_value.standard_header)

        clear_from_asset_cache(asset_uuids=[asset_value.asset_id])

        return Response(serializer.data, status=status.HTTP_200_OK)


class AssetMatchOrCreateView(APIView):
    permission_classes = [IsAuthenticated, IsServiceUser]
    parser_classes = [JSONParser]
    renderer_classes = [JSONRenderer]
    http_method_names = ["post"]

    @extend_schema(
        request=OpenApiRequest(AssetMatchOrCreateRequest),
        responses={
            status.HTTP_200_OK: AssetMatchOrCreateResponse,
            status.HTTP_201_CREATED: AssetMatchOrCreateResponse,
        },
    )
    @atomic()
    def post(self, request):
        """
        Return an existing asset matching the provided values or create a new asset.
        """

        if request.organisation is None:
            raise ValidationError("Organisation not provided")
        try:
            if "imported_from" not in request.data:
                request.data["imported_from"] = None
            to_be_matched = AssetMatchOrCreateRequest.model_validate(request.data)
        except pydantic.ValidationError as e:
            raise ValidationError(str(e))

        # select asset owner organisation
        organisation = request.organisation
        if to_be_matched.organisation_id is not None:
            organisation = Organisations.objects.get(pk=to_be_matched.organisation_id)
            # check user has access to organisation
            if not HasOrganisationAccess().has_object_permission(request, view=self, organisation=organisation):
                raise PermissionDenied(
                    "The target org does not have access to the assets of the specified organisation"
                )

        # if not organisation.is_asset_owner:
        #     raise ValidationError("Organisation not valid for creating or matching assets")

        if not to_be_matched.has_required_for_match:
            asset = self._create_new_asset(to_be_matched, organisation)
            asset_model = AssetSerializer(instance=asset).data
            resp = AssetMatchOrCreateResponse(isMatched=False, asset=asset_model)
            return Response(resp.model_dump(by_alias=True, mode="json"), status=status.HTTP_201_CREATED)

        asset = self._build_matching_query(to_be_matched).first()
        is_matched = asset is not None
        if not is_matched:
            asset = self._create_new_asset(to_be_matched, organisation)

        asset_model = AssetSerializer().to_representation(asset)
        if not is_matched:
            analytics.send_asset_created_event(
                asset=asset_model,
                pipe_type=asset_model.pipe_type,
                creator=None,
                owner_org=asset.organisation,
            )
        resp = AssetMatchOrCreateResponse(isMatched=is_matched, asset=asset_model)
        return Response(
            resp.model_dump(by_alias=True, mode="json"),
            status=status.HTTP_200_OK if is_matched else status.HTTP_201_CREATED,
        )

    def _build_matching_query(self, to_be_matched: AssetMatchOrCreateRequest) -> QuerySet[Asset]:
        organisation_id = to_be_matched.organisation_id or self.request.organisation.id
        header_names_to_values = to_be_matched.asset_value_fields()

        qs = Asset.objects.filter(organisation_id=organisation_id)
        # Exclude assets that are already linked to inspections with a different standard
        qs = qs.exclude(
            Exists(
                Inspection.objects.filter(
                    asset=OuterRef("pk"),
                ).filter(~Q(folder__standard_key=to_be_matched.standard_id))
            )
        )

        if asset_id := header_names_to_values.get("AssetID"):
            # Case-insensitive match on just Asset ID if it is present
            return qs.filter(
                assetvalue__standard_header__header__name="AssetID",
                assetvalue__value__iexact=asset_id,
            )

        for field_name, value in header_names_to_values.items():
            if value is None:
                continue

            qs = qs.filter(assetvalue__standard_header__header__name=field_name, assetvalue__value=str(value))

        return qs

    def _create_new_asset(self, to_be_matched: AssetMatchOrCreateRequest, organisation: Organisations) -> Asset:
        fields_to_create = {k: v for k, v in to_be_matched.asset_value_fields().items() if v is not None}
        creation_payload = {
            **fields_to_create,
            "organisation": organisation.id,
            "type": "pipe",
            "standard": to_be_matched.standard_id,
        }
        serializer = AssetSerializer(
            data=creation_payload,
            context={
                "request": self.request,
                "standard": to_be_matched.standard_id,
            },
        )
        serializer.is_valid(raise_exception=True)
        asset = serializer.save()
        if to_be_matched.imported_from_id:
            import_obj = Import.objects.filter(id=to_be_matched.imported_from_id).first()
            ImportedAsset.objects.create(asset=asset, import_obj=import_obj)

        return asset
