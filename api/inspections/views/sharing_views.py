import base64

from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from django.conf import settings
from django.http import Http404, HttpResponseRedirect, JsonResponse, HttpResponse
from django.shortcuts import get_object_or_404
from django.utils import timezone
from djangorestframework_camel_case.parser import Camel<PERSON>aseJ<PERSON>NParser
from djangorestframework_camel_case.render import Camel<PERSON>aseJ<PERSON>NRenderer
from drf_spectacular.utils import extend_schema
from rest_framework import status
from rest_framework.generics import RetrieveAPIView
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from vapar.constants.exports import ExportFormat
from vapar.core.exports import BulkInspectionPDFExportPayload

from api.actions.models import AuditList
from api.common import encrypt_url
from api.common.permissions import IsAuthenticated, IsStandardUser
from api.common.storage import get_platform_blob_url_with_sas, get_platform_blob_client
from api.exports.models import Export, ExportOutput
from api.exports.serializers import ExportSerializer
from api.exports.sync import run_exports_synchronously
from api.inspections.models import Inspection, FileList, VideoFrames, MapPointList
from api.organisations.models import Organisations, AssetOwners
from api.users.models import CustomUser


class ShareableVideoPlayLink(RetrieveAPIView):
    permission_classes = [AllowAny]
    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]
    queryset = Inspection.objects.all()
    schema = None

    def get(self, request, inspection_id=None):
        """
        Get an external shareable link to play a video
        """
        if request.GET.get("q", "") == encrypt_url.getmyshacode(inspection_id):
            try:
                inspection = self.get_queryset().get(legacy_id=inspection_id)
            except Inspection.DoesNotExist:
                try:
                    inspection = self.get_queryset().get(uuid=inspection_id)
                except Exception:
                    return Http404()
            url = get_platform_blob_url_with_sas(
                blob_path=inspection.file.play_url, region=inspection.file.storage_region
            )
            return HttpResponseRedirect(redirect_to=url)
        else:
            return JsonResponse({"error": "Action not allowed"})


class ShareableVideoDownloadLink(RetrieveAPIView):
    permission_classes = [AllowAny]
    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]
    queryset = FileList.objects.all()
    schema = None

    def get(self, request, token=None):
        """
        Get an external shareable link to download a video
        """
        password = settings.IMAGE_SECRET_KEY
        salt = settings.SALT
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(password))
        f = Fernet(key)
        token = bytes(token, encoding="utf8")
        try:
            id, org = f.decrypt(token).decode("utf-8").split(":")
        except Exception:
            raise Http404

        video = get_object_or_404(self.get_queryset(), pk=id, target_org=org)
        video_url = get_platform_blob_url_with_sas(blob_path=video.url, region=video.storage_region)

        description = "Shareable video download link accessed externally"

        AuditList.objects.create(
            event_type="GET",
            table="FileList",
            column="Multi",
            row_id=id,
            description=description,
            date_of_modification=timezone.now(),
        )

        return HttpResponseRedirect(video_url)


class ShareableImageLink(RetrieveAPIView):
    permission_classes = [AllowAny]
    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]
    queryset = VideoFrames.objects.all()
    schema = None

    def get(self, request, token=None):
        """
        Get an external shareable link to download an image
        """
        # Symetric encryption -- decrypt using keys from settings
        password = settings.IMAGE_SECRET_KEY
        salt = settings.SALT
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(password))
        f = Fernet(key)
        token = bytes(token, encoding="utf8")

        try:
            uid, org = f.decrypt(token).decode("utf-8").split(":")
        except Exception:
            raise Http404

        frame = get_object_or_404(self.get_queryset(), pk=uid, parent_video__target_org=org)

        container, blob_name = frame.image_location.split("/", 1)
        blob_client = get_platform_blob_client(
            blob_name=blob_name, container_name=container, region=frame.parent_video.storage_region
        )
        requested_image = blob_client.download_blob().readall()

        response = HttpResponse(requested_image, content_type="image/png")
        response["Content-Disposition"] = "in-line"

        description = "Shareable image link accessed externally"

        AuditList.objects.create(
            event_type="GET",
            table="VideoFrame",
            column="Multi",
            row_id=uid,
            description=description,
            date_of_modification=timezone.now(),
        )

        return response


class ShareablePDFLink(RetrieveAPIView):
    permission_classes = [AllowAny]
    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]
    schema = None

    def get(self, request, token=None):
        """
        Get an external shareable link to download a PDF
        """

        # Symetric encryption -- decrypt using keys from settings
        password = settings.IMAGE_SECRET_KEY
        salt = settings.SALT
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(password))
        f = Fernet(key)
        token = bytes(token, encoding="utf8")
        # NOTE: unsure of where below was used.
        # domain = request.build_absolute_uri("/")[:-1]

        try:
            vapar_id, org = f.decrypt(token).decode("utf-8").split(":")
        except Exception:
            raise Http404

        # Set org and user to best available values
        request.organisation = Organisations.objects.get(pk=org)
        request.user = CustomUser.objects.filter(organisation=org, is_active=True).first()

        inspection = Inspection.objects.get(legacy_id=vapar_id)

        export_payload = {
            "payload": BulkInspectionPDFExportPayload(
                inspection_ids=[inspection.uuid],
                format=ExportFormat.PDF,
            ).model_dump(mode="json", by_alias=True),
        }
        serializer = ExportSerializer(
            data=export_payload,
            context={"request": request, "user": request.user, "is_initiated_by_user": False},
        )
        serializer.is_valid(raise_exception=True)
        export: Export = serializer.save()

        AuditList.objects.create(
            event_type="Export",
            table="Export",
            column="Multi",
            description=f"1 exports created for organisation {org}",
            date_of_modification=timezone.now(),
            user=request.user,
        )

        successful_exports, failed_exports = run_exports_synchronously(
            exports=[export],
            org=request.organisation,
            max_poll_time_secs=settings.EXTERNAL_INSPECTIONS_PDF_VIEW_TIMEOUT_SECS,
        )
        if failed_exports:
            return Response("Failed to create all exports", status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        successful_export = successful_exports[0]
        export_output = ExportOutput.objects.get(export=successful_export.id)
        sas_url = get_platform_blob_url_with_sas(export_output.blob_url, region=request.organisation.country)
        response = HttpResponseRedirect(sas_url)

        description = "Shareable PDF link accessed externally"

        AuditList.objects.create(
            event_type="GET",
            table="Inspection",
            column="Multi",
            row_id=vapar_id,
            description=description,
            date_of_modification=timezone.now(),
        )
        return response


class InspectionShareableLinks(RetrieveAPIView):
    permission_classes = [IsAuthenticated, IsStandardUser]
    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]

    @extend_schema(responses={status.HTTP_200_OK: dict})
    def get(self, request, id: int):
        """
        Get external shareable links for a given inspection
        """
        inspection = MapPointList.objects.get(id=id)
        vid_uid = encrypt_url.encrypt(inspection.associated_file.id, inspection.associated_file.target_org.id)
        vid_url = encrypt_url.build_encrypted_url(settings.VIDEO_DOMAIN, vid_uid)

        is_ao_exist = AssetOwners.objects.filter(org=request.organisation).exists()

        if is_ao_exist:
            related_contractor_count = request.organisation.assetowners.contractor.count()
            all_related_contractors = request.organisation.assetowners.contractor.all()
            all_contractor_orgs = Organisations.objects.filter(contractors__in=all_related_contractors).values_list(
                "id", flat=True
            )
        else:
            related_contractor_count = 0

        # if the file status in uploaded and the login user is assetowner who wants to access contractor's files
        # we disable the pdf url
        if (
            related_contractor_count > 0
            and inspection.status == "Uploaded"
            and inspection.associated_file.upload_org.id in all_contractor_orgs
        ):
            pdf_url = ""
        else:
            pdf_uid = encrypt_url.encrypt(inspection.id, inspection.associated_file.target_org.id)
            pdf_url = encrypt_url.build_encrypted_url(settings.PDF_DOMAIN, pdf_uid)

        data = {"video_link": vid_url, "pdf_link": pdf_url}

        description = f"Shareable links requested by {request.user.first_name} {request.user.last_name}"

        AuditList.objects.create(
            event_type="GET",
            table="MappointList",
            column="Multi",
            row_id=id,
            description=description,
            date_of_modification=timezone.now(),
            user=request.user,
        )

        return Response(data, status=status.HTTP_200_OK)
