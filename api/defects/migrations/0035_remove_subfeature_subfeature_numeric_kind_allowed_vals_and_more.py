# Generated by Django 4.1.2 on 2025-06-11 03:37

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("defects", "0034_alter_subfeature_feature"),
    ]

    operations = [
        migrations.RemoveConstraint(
            model_name="subfeature",
            name="subfeature_numeric_kind_allowed_vals",
        ),
        migrations.AlterField(
            model_name="subfeature",
            name="numeric_kind",
            field=models.CharField(
                blank=True,
                choices=[
                    ("DIST", "Distance"),
                    ("LEN", "Length"),
                    ("PERC", "Percentage"),
                    ("COUNT", "Count"),
                    ("VOL", "Volume"),
                    ("ANGLE", "Rotational Angle"),
                ],
                max_length=10,
                null=True,
            ),
        ),
        migrations.AddConstraint(
            model_name="subfeature",
            constraint=models.CheckConstraint(
                check=models.Q(
                    models.Q(("kind", "CAT"), ("numeric_kind__isnull", True)),
                    models.Q(
                        ("kind", "NUM"),
                        (
                            "numeric_kind__in",
                            ["DIST", "LEN", "PERC", "COUNT", "VOL", "ANGLE"],
                        ),
                    ),
                    _connector="OR",
                ),
                name="subfeature_numeric_kind_allowed_vals",
            ),
        ),
    ]
