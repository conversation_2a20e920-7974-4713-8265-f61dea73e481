# Generated by Django 4.1.2 on 2025-05-26 14:26

import django.core.validators
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ("defects", "0028_feature_subfeature_subfeatureoption_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="Code",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "code_type",
                    models.CharField(
                        choices=[
                            ("STR", "Structural Defect"),
                            ("SER", "Service Defect"),
                            ("MISC", "Miscellaneous"),
                        ],
                        max_length=10,
                    ),
                ),
                ("display_code", models.CharField(max_length=20)),
                ("display_name", models.CharField(max_length=255)),
                ("at_joint_required", models.BooleanField(default=False)),
                ("continuous_required", models.BooleanField(default=False)),
                ("continuous_allowed", models.<PERSON>oleanField(default=True)),
                ("remarks_required", models.BooleanField(default=False)),
                ("clock_position_from_required", models.BooleanField(default=False)),
                (
                    "clock_position_from_min",
                    models.SmallIntegerField(
                        default=1,
                        validators=[
                            django.core.validators.MinValueValidator(1),
                            django.core.validators.MaxValueValidator(12),
                        ],
                    ),
                ),
                (
                    "clock_position_from_max",
                    models.SmallIntegerField(
                        default=12,
                        validators=[
                            django.core.validators.MinValueValidator(1),
                            django.core.validators.MaxValueValidator(12),
                        ],
                    ),
                ),
                ("clock_position_to_required", models.BooleanField(default=False)),
                (
                    "clock_position_to_min",
                    models.SmallIntegerField(
                        default=1,
                        validators=[
                            django.core.validators.MinValueValidator(1),
                            django.core.validators.MaxValueValidator(12),
                        ],
                    ),
                ),
                (
                    "clock_position_to_max",
                    models.SmallIntegerField(
                        default=12,
                        validators=[
                            django.core.validators.MinValueValidator(1),
                            django.core.validators.MaxValueValidator(12),
                        ],
                    ),
                ),
                ("is_start_code", models.BooleanField(default=False)),
                ("is_end_code", models.BooleanField(default=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "feature",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="codes",
                        to="defects.feature",
                    ),
                ),
                (
                    "standard",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="codes",
                        to="defects.standard",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="CodeScore",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("service_score", models.FloatField(blank=True, null=True)),
                ("structural_score", models.FloatField(blank=True, null=True)),
                ("repair_priority", models.FloatField(blank=True, null=True)),
                (
                    "required_clock_position_min",
                    models.SmallIntegerField(
                        blank=True,
                        null=True,
                        validators=[
                            django.core.validators.MinValueValidator(1),
                            django.core.validators.MaxValueValidator(12),
                        ],
                    ),
                ),
                (
                    "required_clock_position_max",
                    models.SmallIntegerField(
                        blank=True,
                        null=True,
                        validators=[
                            django.core.validators.MinValueValidator(1),
                            django.core.validators.MaxValueValidator(12),
                        ],
                    ),
                ),
                (
                    "required_clock_position_min_spread",
                    models.SmallIntegerField(
                        blank=True,
                        null=True,
                        validators=[
                            django.core.validators.MinValueValidator(1),
                            django.core.validators.MaxValueValidator(12),
                        ],
                    ),
                ),
                (
                    "required_clock_position_max_spread",
                    models.SmallIntegerField(
                        blank=True,
                        null=True,
                        validators=[
                            django.core.validators.MinValueValidator(1),
                            django.core.validators.MaxValueValidator(12),
                        ],
                    ),
                ),
                ("added_service_score_per_metre", models.FloatField(default=0.0)),
                ("added_structural_score_per_metre", models.FloatField(default=0.0)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "code",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="scores",
                        to="defects.code",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="CodeStandardSubcategories",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "code",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to="defects.code"
                    ),
                ),
                (
                    "standard_subcategory",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="defects.standardsubcategory",
                    ),
                ),
            ],
            options={
                "db_table": "service_codestandardsubcategory",
                "unique_together": {("code", "standard_subcategory")},
            },
        ),
        migrations.CreateModel(
            name="CodeScoreStandardSubcategories",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "code_score",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="defects.codescore",
                    ),
                ),
                (
                    "standard_subcategory",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="defects.standardsubcategory",
                    ),
                ),
            ],
            options={
                "db_table": "service_codescorestandardsubcategory",
                "unique_together": {("code_score", "standard_subcategory")},
            },
        ),
        migrations.CreateModel(
            name="CodeScoreRequirement",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "numeric_option_min_breakpoint",
                    models.FloatField(blank=True, null=True),
                ),
                (
                    "numeric_option_max_breakpoint",
                    models.FloatField(blank=True, null=True),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "code_score",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="requirements",
                        to="defects.codescore",
                    ),
                ),
                (
                    "required_option",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="defects.subfeatureoption",
                    ),
                ),
                (
                    "sub_feature",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="defects.subfeature",
                    ),
                ),
            ],
        ),
        migrations.AddField(
            model_name="codescore",
            name="standard_subcategories",
            field=models.ManyToManyField(
                related_name="code_scores",
                through="defects.CodeScoreStandardSubcategories",
                to="defects.standardsubcategory",
            ),
        ),
        migrations.CreateModel(
            name="CodeRequirement",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("numeric_option_range_min", models.FloatField(blank=True, null=True)),
                ("numeric_option_range_max", models.FloatField(blank=True, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "code",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="requirements",
                        to="defects.code",
                    ),
                ),
                (
                    "required_option",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="defects.subfeatureoption",
                    ),
                ),
                (
                    "sub_feature",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="defects.subfeature",
                    ),
                ),
            ],
        ),
        migrations.AddField(
            model_name="code",
            name="standard_subcategories",
            field=models.ManyToManyField(
                related_name="codes",
                through="defects.CodeStandardSubcategories",
                to="defects.standardsubcategory",
            ),
        ),
        migrations.AddConstraint(
            model_name="coderequirement",
            constraint=models.CheckConstraint(
                check=models.Q(
                    models.Q(
                        ("numeric_option_range_max__isnull", True),
                        ("numeric_option_range_min__isnull", True),
                    ),
                    ("required_option__isnull", True),
                    _connector="OR",
                ),
                name="coderequirement_categorical_or_numeric_exclusive",
            ),
        ),
        migrations.AddConstraint(
            model_name="code",
            constraint=models.CheckConstraint(
                check=models.Q(("code_type__in", ["STR", "SER", "MISC"])),
                name="code_code_type_allowed_vals",
            ),
        ),
    ]
