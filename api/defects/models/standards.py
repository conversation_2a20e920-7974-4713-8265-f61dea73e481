from typing import <PERSON><PERSON><PERSON><PERSON>

from django.core.validators import MaxValueValidator, MinValueValidator
from django.db import models
from django_countries.fields import CountryField
import uuid

from api.base.models import Header
from api.defects.models.features import (
    SubFeatureKind,
    SubFeatureNumericKind,
    Feature,
    SubFeature,
    SubFeatureOption,
    SubFeatureUnit,
)


def default_repair_param():
    return {
        "Minimum roots class": 15,
        "Debris build up class": 15,
        "Debris build up length m": 1,
        "Debris single instance class": 20,
        "Patch if score over length >=": 50,
        "Patch length for scoring m": 1,
        "Maximum number of patches over distance": 2,
        "Maximum distance for patch": 30,
        "Maximum number of patches in total": 3,
    }


class Standard(models.Model):
    name = models.CharField(max_length=200, blank=False)
    display_name = models.CharField(max_length=200, blank=False, default="Australia 2020")
    default_repair_param = models.JSONField(blank=True, null=True, default=default_repair_param)

    def __str__(self):
        return str(self.display_name)

    class Meta:
        db_table = "service_scoringstandardslist"


class StandardSubcategory(models.Model):
    standard_key = models.ForeignKey(Standard, on_delete=models.SET_NULL, null=True)
    pipe_type_sewer = models.BooleanField(null=True, blank=True)
    material_type = models.CharField(max_length=100, blank=True, null=True)
    region = CountryField(blank=True, null=True)
    comment = models.CharField(max_length=200, blank=True)

    def __str__(self):
        return str(self.comment)

    class Meta:
        db_table = "service_standard_subcategory"


class _StandardHeaderDataType(models.TextChoices):
    NUMBER = "number", "Number"
    STRING = "string", "String"
    BOOLEAN = "boolean", "Boolean"
    OPTIONS = "options", "Options"
    DATE = "date", "Date"


class StandardHeader(models.Model):
    DataType = _StandardHeaderDataType

    uuid = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    header = models.ForeignKey(Header, on_delete=models.CASCADE)
    standard = models.ForeignKey(Standard, on_delete=models.CASCADE)
    name = models.CharField(max_length=50)
    code = models.CharField(max_length=50, null=True)
    required = models.BooleanField()
    shown_by_default = models.BooleanField()
    description = models.TextField(blank=True, null=True)
    data_type = models.CharField(max_length=7, choices=DataType.choices, null=False)
    options_selections = models.JSONField(blank=True, null=False)
    options_description = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True, blank=False)

    def get_mapped_mpl_field(self):
        return self.header.mapped_mpl_field

    class Meta:
        db_table = "inspections_standardheader"
        constraints = [
            models.CheckConstraint(
                check=models.Q(data_type__in=list(_StandardHeaderDataType)),
                name="standard_header_data_type_allowed_constraint",
            )
        ]


class StandardFeature(models.Model):
    """
    A standard-specific representation of a top-level feature.
    """

    standard = models.ForeignKey(Standard, on_delete=models.CASCADE, related_name="features")
    feature = models.ForeignKey(Feature, on_delete=models.CASCADE, related_name="standardised_set")

    display_name = models.CharField(max_length=255)
    """
    The user-facing name that should be used for the feature under this standard
    """

    created_at = models.DateTimeField(auto_now_add=True)


class StandardSubFeature(models.Model):
    """
    A standard-specific representation of a sub-feature attached to a feature.
    """

    standard = models.ForeignKey(Standard, on_delete=models.CASCADE, related_name="sub_features")
    sub_feature = models.ForeignKey(SubFeature, on_delete=models.CASCADE, related_name="standardised_set")

    display_name = models.CharField(max_length=255)
    """
    The user-facing name that should be used for the sub-feature under this standard
    """

    display_order = models.PositiveIntegerField()
    """
    The order the sub-features should appear in when building a description string for a feature under a code
    """

    default_option = models.ForeignKey(
        SubFeatureOption, on_delete=models.CASCADE, related_name="default_for", null=True, default=None
    )

    numeric_min = models.FloatField(null=True, blank=True)
    """
    Min value for this sub-feature, inclusive
    """
    numeric_max = models.FloatField(null=True, blank=True)
    """
    Max value for this sub-feature, inclusive
    """
    numeric_unit = models.CharField(max_length=50, null=True, blank=True, choices=SubFeatureUnit.choices)
    """
    Unit that this standard uses by default for this feature - if blank, defaults to the base unit for this subfeature
    """

    numeric_default = models.FloatField(null=True, blank=True)
    numeric_display_decimal_places = models.PositiveSmallIntegerField(null=True, blank=True)
    """
    How many decimal places to round to when displaying this sub-feature. Zero to always display as an integer
    """

    quantity_field_number = models.PositiveSmallIntegerField(null=True, default=None)
    """
    Which 'quantity' field this sub feature should be used to populate in reports under this standard
    """
    characteristic_field_number = models.PositiveSmallIntegerField(null=True, default=None)
    """
    Which 'characteristic' field this sub feature should be used to populate in reports under this standard
    """

    maps_numeric_ranges_to_labels = models.BooleanField(default=False)
    """
    Whether this sub feature uses range labels in its representation. Ie. Treating this numeric sub feature
    as categorical
    """

    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"<StandardSubFeature: id={self.id} standard={self.standard} sub_feature={self.sub_feature} display_name='{self.display_name}'>"

    def get_default_numeric_unit(self) -> SubFeatureUnit | None:
        """
        Returns the default numeric unit for this sub-feature under this standard.
        If no specific unit is set, returns the base unit for the sub-feature.
        """
        if self.sub_feature.kind != SubFeatureKind.NUMERIC:
            return None
        if self.numeric_unit:
            return SubFeatureUnit(self.numeric_unit)

        kind = SubFeatureNumericKind(self.sub_feature.numeric_kind)
        return kind.get_base_unit()

    class Meta:
        constraints = [
            # Prevent mixing fields intended only for categorical or numerical variables
            models.CheckConstraint(
                name="standardsubfeature_numeric_xor_categorical",
                check=(
                    models.Q(default_option__isnull=True)  # Case where numerical
                    | models.Q(  # Case where categorical
                        default_option__isnull=False,
                        numeric_default__isnull=True,
                        numeric_display_decimal_places__isnull=True,
                        numeric_unit__isnull=True,
                        numeric_min__isnull=True,
                        numeric_max__isnull=True,
                        maps_numeric_ranges_to_labels=False,
                    )
                ),
            )
        ]


class StandardSubFeatureOption(models.Model):
    """
    A standard-specific representation of an option available for a sub-feature.
    """

    standard = models.ForeignKey(Standard, on_delete=models.CASCADE, related_name="sub_feature_options")
    option = models.ForeignKey(SubFeatureOption, on_delete=models.CASCADE, related_name="standardised_set")
    display_code = models.CharField(max_length=10)
    display_name = models.CharField(max_length=255)
    display_order = models.PositiveIntegerField()

    created_at = models.DateTimeField(auto_now_add=True)


class StandardSubFeatureRangeLabel(models.Model):
    """
    A label to apply to a range on a numeric sub feature, under a standard.

    For cases where the underlying data is numeric, but the standard chooses to represent it as binned categories.
    """

    standard_sub_feature = models.ForeignKey(SubFeature, on_delete=models.CASCADE, related_name="range_labels")
    display_code = models.CharField(max_length=10)
    display_name = models.CharField(max_length=255)
    display_order = models.PositiveIntegerField()

    numeric_range_min = models.FloatField()
    numeric_range_max = models.FloatField()

    created_at = models.DateTimeField(auto_now_add=True)


class CodeStandardSubcategory(models.Model):
    code = models.ForeignKey("Code", on_delete=models.CASCADE)
    standard_subcategory = models.ForeignKey("StandardSubcategory", on_delete=models.CASCADE)

    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = "service_codestandardsubcategory"
        unique_together = ("code", "standard_subcategory")


class CodeType(models.TextChoices):
    STR_DEFECT = "STR", "Structural Defect"
    SER_DEFECT = "SER", "Service Defect"
    MISC = "MISC", "Miscellaneous"


class SubFeatureValue(NamedTuple):
    sub_feature: SubFeature
    numeric_value: float | None
    numeric_range_min: float | None
    numeric_range_max: float | None
    numeric_unit: str | None  # SubFeatureUnit choices
    option: SubFeatureOption | None


class FlagValues(NamedTuple):
    at_joint: bool
    loss_of_vision: bool
    has_textbox: bool
    has_title: bool


class FeatureValue(NamedTuple):
    feature: Feature
    sub_features: list[SubFeatureValue]
    start_chainage: float | None
    end_chainage: float | None
    flag_values: FlagValues
    clock_position_from: int | None
    clock_position_to: int | None
    is_continuous: bool
    remarks: str


class CodeQuerySet(models.QuerySet):
    def for_substandard(self, substd: StandardSubcategory):
        return self.filter(standard_subcategories=substd)

    def prefetch_related_for_matching(self):
        """
        Prefetch related models needed for efficient matching of feature data to codes.
        """

        return self.prefetch_related(
            "requirements",
            "requirements__sub_feature",
            "requirements__required_option",
        )

    def get_applicable_list(self, feature_val: FeatureValue) -> "list[Code]":
        """
        Filter the queryset to a list of codes that can be applied to the provided feature.
        """
        codes_qs = self.filter(feature=feature_val.feature)

        if feature_val.flag_values.at_joint:
            codes_qs = codes_qs.filter(at_joint_allowed=True)
        if feature_val.is_continuous:
            codes_qs = codes_qs.exclude(continuous_allowed=False)
        else:
            codes_qs = codes_qs.filter(continuous_required=False)
        if feature_val.remarks == "":
            codes_qs = codes_qs.filter(remarks_required=False)

        if feature_val.clock_position_from is None:
            codes_qs = codes_qs.filter(clock_position_from_required=False)
        else:
            # Clock position min and max are either not needed or within the range
            codes_qs = codes_qs.filter(
                models.Q(clock_position_from_min__isnull=True)
                | models.Q(clock_position_from_min__lte=feature_val.clock_position_from)
            )
            codes_qs = codes_qs.filter(
                models.Q(clock_position_from_max__isnull=True)
                | models.Q(clock_position_from_max__gte=feature_val.clock_position_from)
            )

        if feature_val.clock_position_to is None:
            codes_qs = codes_qs.filter(clock_position_to_required=False)
        else:
            # Clock position min and max are either not needed or within the range
            codes_qs = codes_qs.filter(
                models.Q(clock_position_to_min__isnull=True)
                | models.Q(clock_position_to_min__lte=feature_val.clock_position_to)
            )
            codes_qs = codes_qs.filter(
                models.Q(clock_position_to_max__isnull=True)
                | models.Q(clock_position_to_max__gte=feature_val.clock_position_to)
            )

        applicable_codes = []
        for code in codes_qs:
            all_reqs_fulfilled = True
            for req in code.requirements.all():
                # Check if the requirement is met by any of the sub-features
                if not any(req.is_met_by_value(sf_val) for sf_val in feature_val.sub_features):
                    all_reqs_fulfilled = False
                    break
            if all_reqs_fulfilled:
                applicable_codes.append(code)

        return applicable_codes


class Code(models.Model):
    """
    A code that can be applied to a given feature if it meets its set of requirements
    """

    CodeType = CodeType

    standard = models.ForeignKey(Standard, on_delete=models.CASCADE, related_name="codes")
    feature = models.ForeignKey(Feature, on_delete=models.CASCADE, related_name="codes")

    code_type = models.CharField(choices=CodeType.choices, max_length=10)

    display_code = models.CharField(max_length=20)
    """The user facing and standard compliant identifier for the classification"""

    display_name = models.CharField(max_length=255)

    standard_subcategories = models.ManyToManyField(
        StandardSubcategory, through="CodeStandardSubcategory", related_name="codes"
    )
    """Which subcategories of the standard this code is applicable to"""

    at_joint_allowed = models.BooleanField(default=False)

    continuous_required = models.BooleanField(default=False)
    continuous_allowed = models.BooleanField(default=True)

    remarks_required = models.BooleanField(default=False)

    clock_position_from_required = models.BooleanField(default=False)
    clock_position_from_min = models.SmallIntegerField(
        default=1, validators=[MinValueValidator(1), MaxValueValidator(12)]
    )
    clock_position_from_max = models.SmallIntegerField(
        default=12, validators=[MinValueValidator(1), MaxValueValidator(12)]
    )

    clock_position_to_required = models.BooleanField(default=False)
    clock_position_to_min = models.SmallIntegerField(
        default=1, validators=[MinValueValidator(1), MaxValueValidator(12)]
    )
    clock_position_to_max = models.SmallIntegerField(
        default=12, validators=[MinValueValidator(1), MaxValueValidator(12)]
    )

    is_start_code = models.BooleanField(default=False)
    """Whether this code is allowed to appear as the first code in an inspection"""
    is_end_code = models.BooleanField(default=False)
    """Whether this code is allowed to appear as the final code in an inspection"""

    created_at = models.DateTimeField(auto_now_add=True)

    objects = CodeQuerySet.as_manager()

    def __str__(self):
        return f"<Code: display_code='{self.display_code}' display_name='{self.display_name}'>"

    def build_description_from_values(
        self, substd: StandardSubcategory, sub_feature_values: list[SubFeatureValue]
    ) -> str | None:
        sub_feature_parts: list[tuple[str, int]] = []
        for val in sub_feature_values:
            part = _build_sub_feature_description_fragment(substd, val)
            if part is None:
                continue
            sub_feature_parts.append(part)

        sub_feature_parts.sort(key=lambda x: x[1])
        sub_feature_descriptions = (x[0] for x in sub_feature_parts)

        all_parts = [self.display_code, self.display_name, *sub_feature_descriptions]
        joined = " - ".join(all_parts)
        return joined

    class Meta:
        constraints = [
            models.CheckConstraint(
                name="code_code_type_allowed_vals",
                check=models.Q(code_type__in=[v for v, _ in CodeType.choices]),
            )
        ]


def _build_sub_feature_description_fragment(
    substd: StandardSubcategory, sub_feature_value: SubFeatureValue
) -> tuple[str, int] | None:
    """
    Returns a description fragment and its order relative to other fragments, or None if the sub-feature is not
    applicable to the sub-standard.
    """

    # Ensure the standardised sub-features are prefetched
    standardised_sf = next(
        (
            std_sf
            for std_sf in sub_feature_value.sub_feature.standardised_set.all()
            if std_sf.standard_id == substd.standard_key_id
        ),
        None,
    )
    if standardised_sf is None:
        return None  # This sub-feature is not applicable to the given standard

    def _format_num(num: float, dp: int | None) -> str:
        if dp is None:
            return str(num)
        else:
            return "{:.{}f}".format(num, dp)

    if sub_feature_value.sub_feature.kind == SubFeatureKind.NUMERIC:
        numeric_unit = SubFeatureUnit(sub_feature_value.numeric_unit)
        decimal_places = standardised_sf.numeric_display_decimal_places

        if sub_feature_value.numeric_value is not None:  # Single value
            formatted_num = _format_num(sub_feature_value.numeric_value, decimal_places)
        else:  # A range
            # TODO: Handle cases where using range labels (StandardSubFeatureRangeLabel)
            lower_bound = (
                _format_num(sub_feature_value.numeric_range_min, decimal_places)
                if sub_feature_value.numeric_range_min is not None
                else None
            )
            upper_bound = (
                _format_num(sub_feature_value.numeric_range_max, decimal_places)
                if sub_feature_value.numeric_range_max is not None
                else None
            )
            if lower_bound is None and lower_bound is None:
                raise ValueError("Neither end of the range is present")
            if lower_bound is None:
                formatted_num = "<" + upper_bound
            elif upper_bound is None:
                formatted_num = ">" + lower_bound
            else:
                formatted_num = f"{lower_bound}-{upper_bound}"

        formatted_with_suffix = formatted_num + numeric_unit.get_display_suffix()

        return formatted_with_suffix, standardised_sf.display_order

    else:  # CATEGORICAL
        if sub_feature_value.option is None:
            return None
        # Ensure the standard sub feature options are prefetched
        standardised_option = next(
            (
                opt
                for opt in sub_feature_value.option.standardised_set.all()
                if opt.standard_id == substd.standard_key_id
            ),
            None,
        )
        if not standardised_option:
            return None  # No standard-specific representation for this option

        return standardised_option.display_name, standardised_sf.display_order


class CodeRequirement(models.Model):
    """
    A requirement for a code to be applicable to a feature
    """

    code = models.ForeignKey(Code, on_delete=models.CASCADE, related_name="requirements")
    sub_feature = models.ForeignKey(SubFeature, on_delete=models.CASCADE, null=True)
    required_option = models.ForeignKey(SubFeatureOption, on_delete=models.CASCADE, null=True)

    numeric_option_range_min = models.FloatField(null=True, blank=True)
    numeric_option_range_max = models.FloatField(null=True, blank=True)

    created_at = models.DateTimeField(auto_now_add=True)

    def is_met_by_value(self, value: SubFeatureValue) -> bool:
        """
        Checks if the provided sub-feature value meets the requirement.
        """
        if self.sub_feature and self.sub_feature.id != value.sub_feature.id:
            return False

        if self.sub_feature.kind == SubFeatureKind.CATEGORICAL:
            if self.required_option:
                if not value.option or self.required_option.id != value.option.id:
                    return False

        else:  # Numeric sub-feature
            value_unit = SubFeatureUnit(value.numeric_unit)

            numeric_kind = SubFeatureNumericKind(self.sub_feature.numeric_kind)
            # NOTE: This assumes all requirements are expressed in base units.
            # Should these be in terms of the standardised version's unit instead?
            # (StandardSubFeature.get_default_numeric_unit)
            base_unit = numeric_kind.get_base_unit()

            lower_num = value.numeric_range_min if value.numeric_value is None else value.numeric_value
            lower_num_normalised = value_unit.convert_to_unit(lower_num, base_unit) if lower_num is not None else None

            upper_num = value.numeric_range_max if value.numeric_value is None else value.numeric_value
            upper_num_normalised = value_unit.convert_to_unit(upper_num, base_unit) if upper_num is not None else None

            if self.numeric_option_range_min is not None:
                if lower_num_normalised is None or lower_num_normalised < self.numeric_option_range_min:
                    return False
            if self.numeric_option_range_max is not None:
                if upper_num_normalised is None or upper_num_normalised > self.numeric_option_range_max:
                    return False

        return True

    class Meta:
        constraints = [
            models.CheckConstraint(
                name="coderequirement_categorical_or_numeric_exclusive",
                check=(
                    models.Q(numeric_option_range_min__isnull=True, numeric_option_range_max__isnull=True)
                    | models.Q(required_option__isnull=True)
                ),
            )
        ]


class CodeScoreStandardSubcategory(models.Model):
    code_score = models.ForeignKey("CodeScore", on_delete=models.CASCADE)
    standard_subcategory = models.ForeignKey("StandardSubcategory", on_delete=models.CASCADE)

    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = "service_codescorestandardsubcategory"
        unique_together = ("code_score", "standard_subcategory")


class CodeScore(models.Model):
    """A set of scores that are applied to a given code under particular substandards, given that their ranges are met"""

    standard_subcategories = models.ManyToManyField(
        StandardSubcategory, through="CodeScoreStandardSubcategory", related_name="code_scores"
    )

    code = models.ForeignKey(Code, on_delete=models.CASCADE, related_name="scores")

    service_score = models.FloatField(null=True, blank=True)
    structural_score = models.FloatField(null=True, blank=True)
    repair_priority = models.FloatField(null=True, blank=True)

    # Note that some PACP codes use clock position for determining score
    required_clock_position_min = models.SmallIntegerField(
        null=True, blank=True, validators=[MinValueValidator(1), MaxValueValidator(12)]
    )
    required_clock_position_max = models.SmallIntegerField(
        null=True, blank=True, validators=[MinValueValidator(1), MaxValueValidator(12)]
    )

    required_clock_position_min_spread = models.SmallIntegerField(
        null=True, blank=True, validators=[MinValueValidator(1), MaxValueValidator(12)]
    )
    required_clock_position_max_spread = models.SmallIntegerField(
        null=True, blank=True, validators=[MinValueValidator(1), MaxValueValidator(12)]
    )

    added_service_score_per_metre = models.FloatField(default=0.0)
    """The amount to add to the service score for each metre a continuous defect extends."""
    added_structural_score_per_metre = models.FloatField(default=0.0)
    """The amount to add to the structural score for each metre a continuous defect extends."""

    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"<CodeScore: code='{self.code.display_code}' svc='{self.service_score}' str='{self.structural_score}' rp='{self.repair_priority}'>"


class CodeScoreRequirement(models.Model):
    """
    A requirement that must be met for a set of scores to be applicable to a code, under particular sub-standards
    """

    code_score = models.ForeignKey(CodeScore, on_delete=models.CASCADE, related_name="requirements")
    sub_feature = models.ForeignKey(SubFeature, on_delete=models.CASCADE, null=True)
    required_option = models.ForeignKey(SubFeatureOption, on_delete=models.CASCADE, null=True)

    numeric_option_min_breakpoint = models.FloatField(null=True, blank=True)
    """Min value for this sub-feature, inclusive"""
    numeric_option_max_breakpoint = models.FloatField(null=True, blank=True)
    """Max value for this sub-feature, inclusive"""

    created_at = models.DateTimeField(auto_now_add=True)
