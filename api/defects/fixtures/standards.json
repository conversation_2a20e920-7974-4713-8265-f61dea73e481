[{"model": "defects.standard", "pk": 1, "fields": {"name": "WSA-05 2013", "display_name": "Australia 2013", "default_repair_param": {"Minimum roots class": 15, "Debris build up class": 15, "Debris build up length m": 1, "Maximum distance for patch": 30, "Patch length for scoring m": 1, "Debris single instance class": 20, "Patch if score over length >=": 50, "Maximum number of patches in total": 3, "Maximum number of patches over distance": 2}}}, {"model": "defects.standard", "pk": 2, "fields": {"name": "MSCC5", "display_name": "UK V5", "default_repair_param": {"Minimum roots class": 15, "Debris build up class": 15, "Debris build up length m": 1, "Maximum distance for patch": 30, "Patch length for scoring m": 1, "Debris single instance class": 20, "Patch if score over length >=": 50, "Maximum number of patches in total": 3, "Maximum number of patches over distance": 2}}}, {"model": "defects.standard", "pk": 3, "fields": {"name": "PACP7", "display_name": "US V7", "default_repair_param": {"Minimum roots class": 15, "Debris build up class": 15, "Debris build up length m": 1, "Maximum distance for patch": 30, "Patch length for scoring m": 3, "Debris single instance class": 20, "Patch if score over length >=": 4, "Maximum number of patches in total": 3, "Maximum number of patches over distance": 2}}}, {"model": "defects.standard", "pk": 4, "fields": {"name": "NZ Pipe Manual 3", "display_name": "NZ V3", "default_repair_param": {"Minimum roots class": 15, "Debris build up class": 15, "Debris build up length m": 1, "Maximum distance for patch": 30, "Patch length for scoring m": 1, "Debris single instance class": 20, "Patch if score over length >=": 50, "Maximum number of patches in total": 3, "Maximum number of patches over distance": 2}}}, {"model": "defects.standard", "pk": 5, "fields": {"name": "NZ Pipe Manual 4", "display_name": "NZ V4", "default_repair_param": {"Minimum roots class": 15, "Debris build up class": 15, "Debris build up length m": 1, "Maximum distance for patch": 30, "Patch length for scoring m": 1, "Debris single instance class": 20, "Patch if score over length >=": 50, "Maximum number of patches in total": 3, "Maximum number of patches over distance": 2}}}, {"model": "defects.standard", "pk": 6, "fields": {"name": "WSA-05 2020", "display_name": "Australia 2020", "default_repair_param": {"Minimum roots class": 15, "Debris build up class": 15, "Debris build up length m": 1, "Maximum distance for patch": 30, "Patch length for scoring m": 1, "Debris single instance class": 20, "Patch if score over length >=": 50, "Maximum number of patches in total": 3, "Maximum number of patches over distance": 2}}}, {"model": "defects.standard", "pk": 7, "fields": {"name": "PACP8", "display_name": "US V8", "default_repair_param": {"Minimum roots class": 15, "Debris build up class": 15, "Debris build up length m": 1, "Maximum distance for patch": 30, "Patch length for scoring m": 3, "Debris single instance class": 20, "Patch if score over length >=": 4, "Maximum number of patches in total": 3, "Maximum number of patches over distance": 2}}}, {"model": "defects.standard", "pk": 8, "fields": {"name": "Japan VS", "display_name": "Japan VS", "default_repair_param": {"Minimum roots class": 15, "Debris build up class": 15, "Debris build up length m": 1, "Maximum distance for patch": 30, "Patch length for scoring m": 3, "Debris single instance class": 20, "Patch if score over length >=": 4, "Maximum number of patches in total": 3, "Maximum number of patches over distance": 2}}}]