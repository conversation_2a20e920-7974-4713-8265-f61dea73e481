from pathlib import Path

from django.core.management.base import BaseCommand
from django.db import transaction, connection
import pandas as pd

from api.base.models import Header
from api.defects.models import DefectModelList, DefectScores, StandardSubcategory, Standard, StandardHeader


MANAGEMENT_FOLDER = Path(__file__).parent
DATA_FOLDER = MANAGEMENT_FOLDER.parent / "baseline_data"


def create_headers_from_template(file_name, std_obj):
    df = pd.read_excel(DATA_FOLDER / file_name, engine="openpyxl", sheet_name="Standard details")
    df = df.replace({float("nan"): ""})
    records = df.to_dict("records")

    for record in records:
        record["Options_Selection"] = [
            str(option) for option in record["Options_Selection"].replace(" ", "").split(",")
        ]

    model_instances = [
        StandardHeader(
            name=record["Name"],
            code=record["Code"],
            data_type=record["DataType"],
            required=record["Required"],
            shown_by_default=record["Shown_By_Default"],
            description=record["Description"],
            options_selections=record["Options_Selection"],
            options_description=record["Options_Description"],
            header=Header.objects.filter(name=record["Header__name"]).first(),
            standard=std_obj,
        )
        for record in records
    ]
    print(model_instances)

    StandardHeader.objects.bulk_create(model_instances)


def create_defects_from_template(file_name, std_obj, sub_std_obj):
    df = pd.read_excel(DATA_FOLDER / file_name, engine="openpyxl", sheet_name="Standard details")
    df = df.replace({float("nan"): ""})
    records = df.to_dict("records")

    model_instances = [
        DefectScores(
            defect_description=record["Defect_description"]
            if record["Defect_code"] == ""
            else f"{record['Defect_code']} - {record['Defect_description']}",
            defect_type=record["Defect_type"],
            defect_code=record["Defect_code"],
            structural_score=record["Structural_score"],
            service_score=record["Service_score"],
            is_shown=record["is_shown"],
            main_category=record["main_category"],
            characterisation_1=record["characterisation_1"] if record["characterisation_1"] else None,
            characterisation_2=record["characterisation_2"] if record["characterisation_2"] else None,
            quantity_1_desc=record["quantity_1_desc"] if record["quantity_1_desc"] else None,
            quantity_1_default_val=record["quantity_1_default_val"] if record["quantity_1_default_val"] else None,
            quantity_1_start_val=record["quantity_1_start_val"] if record["quantity_1_start_val"] else None,
            quantity_1_end_val=record["quantity_1_end_val"] if record["quantity_1_end_val"] else None,
            quantity_1_score_type=record["quantity_1_score_type"] if record["quantity_1_score_type"] else None,
            quantity_1_units=record["quantity_1_units"] if record["quantity_1_units"] else None,
            quantity_2_desc=record["quantity_2_desc"] if record["quantity_2_desc"] else None,
            quantity_2_default_val=record["quantity_2_default_val"] if record["quantity_2_default_val"] else None,
            quantity_2_start_val=record["quantity_2_start_val"] if record["quantity_2_start_val"] else None,
            quantity_2_end_val=record["quantity_2_end_val"] if record["quantity_2_end_val"] else None,
            quantity_2_score_type=record["quantity_2_score_type"] if record["quantity_2_score_type"] else None,
            quantity_2_units=record["quantity_2_units"] if record["quantity_2_units"] else None,
            percentage_required=record["percentage_required"],
            at_joint_required=record["at_joint_required"],
            clock_position_required=record["clock_position_required"],
            clock_spread_possible=record["clock_spread_possible"],
            continuous_score=record["continuous_score"],
            material_applied=record["material_applied"] if record["material_applied"] else None,
            start_survey=record["start_survey"],
            end_survey=record["end_survey"],
            fastpass_code=record["fastpass_code"],
            repair_category=record["repair_category"] if record["repair_category"] else None,
            repair_priority=record["repair_priority"],
            defect_key=DefectModelList.objects.filter(name=record["defect_key__name"]).first(),
            standard_key=std_obj,
            sub_standard=sub_std_obj,
        )
        for record in records
    ]

    DefectScores.objects.bulk_create(model_instances)


def create_standard_and_sub(name, display_name, default_repair_param):
    reset_id_sequence("service_scoringstandardslist")
    reset_id_sequence("service_standard_subcategory")

    std_obj = Standard.objects.create(name=name, display_name=display_name, default_repair_param=default_repair_param)

    sewer_sub = StandardSubcategory.objects.create(
        pipe_type_sewer=True, material_type="Rigid", comment=display_name + " Sewer Rigid", standard_key=std_obj
    )
    storm_sub = StandardSubcategory.objects.create(
        pipe_type_sewer=False, material_type="Rigid", comment=display_name + " Stormwater Rigid", standard_key=std_obj
    )

    return std_obj, sewer_sub, storm_sub


def remove_duplicate_dml_entries():
    ids = [
        172,
        173,
        175,
        176,
        178,
        180,
        188,
        190,
        192,
        267,
        268,
        270,
        271,
        273,
        275,
        276,
        278,
        280,
        281,
        284,
        286,
        289,
        291,
        294,
        296,
        299,
        302,
        304,
        306,
        309,
        312,
        315,
        316,
        319,
        322,
        324,
        327,
        329,
        331,
        538,
        539,
        550,
        551,
        552,
        553,
        554,
        555,
        556,
        557,
        558,
        566,
        567,
        568,
        569,
        570,
        571,
        572,
    ]
    dml_objs = DefectModelList.objects.filter(id__in=ids)
    dml_objs.delete()


def reset_id_sequence(table_name: str):
    cursor = connection.cursor()
    cursor.execute(
        f"""SELECT setval(pg_get_serial_sequence('{table_name}', 'id'), coalesce(MAX(id), 1))
        FROM {table_name};"""
    )


class Command(BaseCommand):
    help = "Create a new standard in the platform from import files."

    def add_arguments(self, parser):
        # parser.add_argument("dry_run", type=bool)
        pass

    def handle(self, *args, **options):
        """Handle the command execution."""

        header_template_file = "PACP8_headers.xlsx"
        defect_template_file = "PACP8_defects.xlsx"
        name = "PACP8"
        display_name = "US V8"
        default_repair_param = {
            "Minimum roots class": 15,
            "Debris build up class": 15,
            "Debris build up length m": 1,
            "Debris single instance class": 20,
            "Patch if score over length >=": 4,
            "Patch length for scoring m": 3,
            "Maximum number of patches over distance": 2,
            "Maximum distance for patch": 30,
            "Maximum number of patches in total": 3,
        }

        if Standard.objects.filter(name=name).exists():
            print(f"Standard with name '{name}' already exists.")
            return

        with transaction.atomic():
            remove_duplicate_dml_entries()
            std_obj, sewer_sub, storm_sub = create_standard_and_sub(name, display_name, default_repair_param)
            create_headers_from_template(header_template_file, std_obj)
            create_defects_from_template(defect_template_file, std_obj, sewer_sub)
            create_defects_from_template(defect_template_file, std_obj, storm_sub)
