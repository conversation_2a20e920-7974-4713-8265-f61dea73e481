import io
import csv

from django.conf import settings
from django.http import StreamingHttpResponse
from django.db.models import Case, When, Value, IntegerField
from django.utils.decorators import method_decorator
from django.views.decorators.cache import cache_page
from django_filters.rest_framework import DjangoFilter<PERSON><PERSON>end
from drf_spectacular.types import OpenApiTypes
from drf_spectacular.utils import extend_schema, OpenApiParameter

from rest_framework import exceptions, status
from rest_framework.generics import ListAPIView, get_object_or_404

from djangorestframework_camel_case.parser import CamelCaseJSONParser
from djangorestframework_camel_case.render import Camel<PERSON>aseJ<PERSON>NRenderer
from rest_framework.response import Response

from api.common.permissions import IsStandardUser, IsAuthenticated, IsServiceUser
from api.defects.models import DefectScores, Standard, StandardHeader, StandardSubcategory, Code, Feature
from api.defects.serializers import (
    AllDefectsSerializer,
    DefectSerializer,
    StandardSerializer,
    StandardHeaderSerializer,
    StandardSubcategorySerializer,
    enumerate_code_variants,
    CodeVariantsSerializer,
    FeatureFullDetailSerializer,
)
from api.inspections.models import MapPointList


class StandardList(ListAPIView):
    permission_classes = [IsAuthenticated]
    serializer_class = StandardSerializer
    queryset = Standard.objects.all().order_by("display_name")

    def get(self, request):
        """
        Get a list of standards
        """
        self.queryset = self.get_queryset().order_by("display_name")
        return super().list(request)


class StandardSubcategoryList(ListAPIView):
    permission_classes = [IsAuthenticated, IsStandardUser]
    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]

    queryset = StandardSubcategory.objects.select_related("standard_key").order_by("id").all()
    serializer_class = StandardSubcategorySerializer
    filter_backends = [DjangoFilterBackend]
    filterset_fields = [
        "standard_key_id",
        "standard_key__name",
        "standard_key__display_name",
        "material_type",
        "pipe_type_sewer",
    ]

    def get(self, request, *args, **kwargs):
        """
        List subcategories for standards

        Filter by standard_key_id to list subcategories for a specific standard
        """
        return super().get(request, *args, **kwargs)


class StandardHeaderList(ListAPIView):
    permission_classes = [IsAuthenticated, IsStandardUser]
    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]
    serializer_class = StandardHeaderSerializer
    queryset = StandardHeader.objects.all().order_by("created_at")
    pagination_class = None

    @extend_schema(
        parameters=[
            OpenApiParameter("standard_id", OpenApiTypes.INT, required=True, description="Standard ID"),
            OpenApiParameter("format", OpenApiTypes.STR, required=False, description="Output format"),
        ],
        responses={
            (status.HTTP_200_OK, "application/json"): StandardHeaderSerializer(many=True),
            (status.HTTP_200_OK, "text/csv"): OpenApiTypes.STR,
        },
    )
    def get(self, request, *args, **kwargs):
        """
        Get a list of headers for a standard.
        """
        standard_id = request.query_params.get("standard_id", None)

        if not standard_id:
            raise exceptions.ValidationError("You must supply a standard_id to get headers")

        standard = Standard.objects.filter(id=standard_id).first()

        if not standard:
            raise exceptions.ValidationError("Invalid standard_id")

        self.queryset = self.queryset.filter(standard=standard_id)

        format = kwargs.get("format", None)

        if format and format == "csv":
            file_name = f"{standard.name}_csv_template"
            headers = [item.name for item in self.queryset]

            with io.StringIO() as f:
                dict_writer = csv.DictWriter(f, headers)
                dict_writer.writeheader()
                csv_contents = f.getvalue()

                file_response = StreamingHttpResponse(
                    (csv_contents),
                    content_type="text/csv",
                    headers={"Content-Disposition": f"attachment; filename={file_name}.csv"},
                )

            return file_response

        self.queryset = self.queryset.filter(shown_by_default=True)

        headers = [
            "AssetID",
            "LocationStreet",
            "LocationTown",
            "UpstreamNode",
            "DownstreamNode",
            "Material",
            "HeightDiameter",
            "UseOfDrainSewer",
            "Date",
            "Time",
            "Direction",
            "StartNodeRef",
            "Node1Ref",
            "FinishNodeRef",
            "LengthSuveyed",
            "IsImperial",
            "MethodOfInspection",
            "GeneralRemarks",
        ]

        preserved = Case(
            *[When(header__name=header, then=Value(pos)) for pos, header in enumerate(headers)],
            output_field=IntegerField(),
        )
        self.queryset = self.queryset.annotate(sort_order=preserved).order_by("sort_order")

        return super().list(request, *args, **kwargs)


class StandardHeaderCsv(StandardHeaderList):
    @extend_schema(
        parameters=[
            OpenApiParameter("standard_id", OpenApiTypes.INT, required=True, description="Standard ID"),
        ],
        responses={(status.HTTP_200_OK, "text/csv"): OpenApiTypes.STR},
    )
    def get(self, request, *args, **kwargs):
        """
        Get a csv output of the headers for a standard.
        """
        kwargs["format"] = "csv"
        return super().get(request, *args, **kwargs)


class AllDefectsList(ListAPIView):
    permission_classes = [IsAuthenticated, IsServiceUser]
    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]
    pagination_class = None

    queryset = DefectScores.objects.all().order_by("id")
    serializer_class = AllDefectsSerializer
    filter_backends = [DjangoFilterBackend]
    filterset_fields = [
        "standard_key__id",
        "standard_key__name",
        "defect_description",
        "defect_key__name",
        "defect_key__id",
        "sub_standard__id",
        "sub_standard__comment",
        "sub_standard__pipe_type_sewer",
        "sub_standard__material_type",
        "is_shown",
    ]

    def get(self, request, *args, **kwargs):
        """
        Get a list of all items from the defectscores table
        """
        return super().get(request, *args, **kwargs)


class DefectList(ListAPIView):
    pagination_class = None
    permission_classes = [IsAuthenticated, IsStandardUser]
    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]
    serializer_class = DefectSerializer
    queryset = DefectScores.objects.all().order_by("defect_description")

    @extend_schema(
        "standards_defects_list_by_inspection",
    )
    def get(self, request, *args, **kwargs):
        """
        Get a list of defects for the standard of the inspection
        """
        queryset = self.get_queryset()

        inspection = MapPointList.objects.get(id=kwargs["inspection_id"])

        self.queryset = (
            queryset.filter(
                sub_standard__standard_key=inspection.standard_key,
                sub_standard__material_type="Rigid",
                sub_standard__pipe_type_sewer=inspection.sewer_data,
            )
            .order_by("defect_description")
            .select_related("defect_key")
            .distinct("defect_description")
        )

        return super().list(request, *args, *kwargs)


class CodeVariantsList(ListAPIView):
    pagination_class = None

    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]

    permission_classes = [IsAuthenticated]

    serializer_class = CodeVariantsSerializer

    queryset = Code.objects.all().order_by("display_code", "id")

    def get_queryset(self):
        substd = get_object_or_404(StandardSubcategory, pk=self.kwargs.get("standard_subcategory_id"))
        qs = super().get_queryset().filter(codestandardsubcategories__standard_subcategory=substd)
        return qs

    def list(self, request, *args, **kwargs):
        self.check_permissions(request)
        substd = get_object_or_404(StandardSubcategory, pk=self.kwargs.get("standard_subcategory_id"))
        codes_qs = self.get_queryset()
        res_serializers = [enumerate_code_variants(code, substd) for code in codes_qs]
        return Response(data=[ser.data for ser in res_serializers])

    @method_decorator(cache_page(settings.CODE_VARIANTS_LIST_CACHE_TTL_SECS, cache="view-cache"))
    def get(self, request, *args, **kwargs):
        """List all the variations of codes that could be applied to observations under a standard subcategory"""
        return super().get(request, *args, **kwargs)


class FeatureList(ListAPIView):
    pagination_class = None

    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]

    permission_classes = [IsAuthenticated, IsServiceUser]

    serializer_class = FeatureFullDetailSerializer

    queryset = Feature.objects.prefetch_related(
        "mapped_ml_labels",
        "sub_features",
        "sub_features__options",
        "sub_features__mapped_sub_feature_ml_labels",
    )

    @method_decorator(cache_page(settings.FEATURES_LIST_CACHE_TTL_SECS, cache="view-cache"))
    def get(self, request, *args, **kwargs):
        """Service endpoint for listing full details of supported inspection features"""
        return super().get(request, *args, **kwargs)
