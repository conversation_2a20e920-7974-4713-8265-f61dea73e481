from django.db.models import QuerySet
from rest_framework import serializers

from .models import (
    DefectScores,
    Standard,
    StandardHeader,
    StandardSubcategory,
    Code,
    CodeRequirement,
    Feature,
    SubFeature,
    CodeScore,
    CodeScoreRequirement,
    SubFeatureOption,
    SubFeatureValue,
)
from .models import MLLabelFeatureMapping, MLLabelSubFeatureMapping
from ..base.models import Header


class DefectSerializer(serializers.ModelSerializer):
    defect_label = serializers.SerializerMethodField(read_only=True)

    def get_defect_label(self, obj) -> str | None:
        if obj.defect_description and obj.defect_code:
            if obj.defect_code not in obj.defect_description:
                return f"{obj.defect_code} - {obj.defect_description}"
            return obj.defect_description
        return None

    class Meta:
        model = DefectScores
        fields = [
            "id",
            "defect_code",
            "defect_description",
            "defect_label",
            "clock_position_required",
            "clock_spread_possible",
            "at_joint_required",
            "percentage_required",
            "start_survey",
            "end_survey",
        ]


class StandardSerializer(serializers.ModelSerializer):
    class Meta:
        model = Standard
        fields = ["id", "display_name", "name"]


class StandardSubcategorySerializer(serializers.ModelSerializer):
    standard = StandardSerializer(read_only=True, source="standard_key")
    region = serializers.SerializerMethodField()

    def get_region(self, data) -> str:
        return data.region.code

    class Meta:
        model = StandardSubcategory
        fields = ["id", "material_type", "pipe_type_sewer", "comment", "standard", "region"]


class StandardHeaderSerializer(serializers.ModelSerializer):
    options_description = serializers.SerializerMethodField(read_only=True)
    type = serializers.SerializerMethodField(read_only=True)
    mapped_field_name = serializers.SerializerMethodField(read_only=True)
    display_name = serializers.SerializerMethodField(read_only=True)

    def get_options_description(self, obj) -> list[str]:
        if obj.options_description:
            return [x.strip() for x in obj.options_description.split(",")]
        return []

    def get_type(self, obj) -> Header.HeaderType:
        return obj.header.type

    def get_mapped_field_name(self, obj) -> str | None:
        if obj.header.mapped_mpl_field and obj.header.mapped_mpl_field != obj.header.name:
            return obj.header.mapped_mpl_field
        return None

    def get_display_name(self, obj) -> str | None:
        if obj.header.display_name:
            return obj.header.display_name
        return obj.header.name

    class Meta:
        model = StandardHeader
        fields = [
            "name",
            "required",
            "description",
            "type",
            "data_type",
            "options_selections",
            "options_description",
            "mapped_field_name",
            "display_name",
        ]


class AllDefectsSerializer(serializers.ModelSerializer):
    substandard = StandardSubcategorySerializer(read_only=True, source="sub_standard")
    defect_model_name = serializers.SerializerMethodField(read_only=True)
    defect_model_id = serializers.SerializerMethodField(read_only=True)

    def get_defect_model_name(self, obj) -> str | None:
        if obj.defect_key:
            if obj.defect_key.name:
                return obj.defect_key.name
        return None

    def get_defect_model_id(self, obj) -> int | None:
        if obj.defect_key:
            return obj.defect_key.id
        return None

    class Meta:
        model = DefectScores
        fields = [
            "id",
            "substandard",
            "defect_description",
            "defect_model_name",
            "defect_model_id",
            "service_score",
            "structural_score",
            "defect_type",
            "quantity_1_default_val",
            "quantity_1_units",
            "quantity_2_default_val",
            "quantity_2_units",
            "defect_code",
            "at_joint_required",
            "material_applied",
            "characterisation_1",
            "characterisation_2",
            "clock_position_required",
            "clock_spread_possible",
            "percentage_required",
            "start_survey",
            "end_survey",
            "repair_priority",
            "repair_category",
            "fastpass_code",
            "continuous_score",
        ]


class MLLabelFeatureMappingSerializer(serializers.ModelSerializer):
    class Meta:
        model = MLLabelFeatureMapping
        fields = [
            "feature_id",
            "ml_label",
        ]


class MLLabelSubFeatureMappingSerializer(serializers.ModelSerializer):
    class Meta:
        model = MLLabelSubFeatureMapping
        fields = [
            "sub_feature_id",
            "ml_label",
            "option_id",
            "numeric_unit",
            "numeric_value",
            "numeric_min",
            "numeric_max",
        ]


class SubFeatureOptionSerializer(serializers.ModelSerializer):
    class Meta:
        model = SubFeatureOption
        fields = [
            "id",
            "key",
            "display_name",
            "display_order",
            "sub_feature_id",
        ]


class SubFeatureFullDetailSerializer(serializers.ModelSerializer):
    mapped_ml_labels = MLLabelSubFeatureMappingSerializer(many=True)
    options = SubFeatureOptionSerializer(many=True)

    class Meta:
        model = SubFeature
        fields = [
            "id",
            "feature_id",
            "key",
            "display_name",
            "display_order",
            "kind",
            "numeric_kind",
            "options",
            "mapped_ml_labels",
        ]


class FeatureFullDetailSerializer(serializers.ModelSerializer):
    sub_features = SubFeatureFullDetailSerializer(many=True)
    mapped_ml_labels = MLLabelFeatureMappingSerializer(many=True)

    class Meta:
        model = Feature
        fields = [
            "id",
            "key",
            "display_name",
            "sub_features",
            "mapped_ml_labels",
        ]


class FeatureSerializer(serializers.ModelSerializer):
    class Meta:
        model = Feature
        fields = [
            "id",
            "key",
            "display_name",
        ]


class SubFeatureSerializer(serializers.ModelSerializer):
    class Meta:
        model = SubFeature
        fields = [
            "id",
            "feature_id",
            "key",
            "display_name",
            "display_order",
            "kind",
            "numeric_kind",
        ]


class CodeSerializer(serializers.ModelSerializer):
    class Meta:
        model = Code
        fields = [
            "id",
            "standard_id",
            "code_type",
            "display_code",
            "display_name",
            "at_joint_allowed",
            "continuous_required",
            "continuous_allowed",
            "remarks_required",
            "clock_position_from_required",
            "clock_position_from_min",
            "clock_position_from_max",
            "clock_position_to_required",
            "clock_position_to_min",
            "clock_position_to_max",
            "is_start_code",
            "is_end_code",
        ]


class CodeRequirementSerializer(serializers.ModelSerializer):
    class Meta:
        model = CodeRequirement
        fields = [
            "id",
            "code_id",
            "sub_feature_id",
            "required_option_id",
            "numeric_option_range_max",
            "numeric_option_range_min",
        ]


def enumerate_code_variants(code: Code, substandard: StandardSubcategory) -> "CodeVariantsSerializer":
    """
    List combinations of values that correspond to a scored code under a sub-standard

    :returns A serializer populated with data
    """

    # Note: Handle prefetching in the view instead, if doing this per code turns out to be too slow
    scores_qs = code.scores.filter(standard_subcategories=substandard).prefetch_related(
        "requirements",
        "requirements__required_option",
        "requirements__sub_feature",
        "requirements__sub_feature__standardised_set",
        "requirements__sub_feature__options",
        "requirements__sub_feature__options__standardised_set",
    )
    requirements_qs = code.requirements.select_related("required_option").prefetch_related(
        "sub_feature",
        "sub_feature__standardised_set",
        "sub_feature__options",
        "sub_feature__options__standardised_set",
    )

    values_required_for_code = _build_example_values_from_code_requirements(requirements_qs, substandard)
    variants = _build_code_variants_serializer_data(values_required_for_code, code, scores_qs, substandard)

    variants_data = {"code": code, "code_requirements": requirements_qs, "variants": variants}
    serializer = CodeVariantsSerializer(variants_data)
    return serializer


def _build_example_values_from_code_requirements(
    reqs_qs: QuerySet[CodeRequirement], substandard: StandardSubcategory
) -> list[SubFeatureValue]:
    values = []
    for req in reqs_qs:
        try:
            standardised_sf = next(
                sf for sf in req.sub_feature.standardised_set.all() if sf.standard_id == substandard.standard_key_id
            )
        except StopIteration:
            raise ValueError(
                f"The sub-feature {req.sub_feature} does not have a standard representation for the"
                f" substandard {substandard}."
            )
        numeric_unit = standardised_sf.get_default_numeric_unit()
        values.append(
            SubFeatureValue(
                sub_feature=req.sub_feature,
                numeric_value=None,
                numeric_range_max=req.numeric_option_range_max,
                numeric_range_min=req.numeric_option_range_min,
                numeric_unit=numeric_unit,
                option=req.required_option,
            )
        )
    return values


def _build_code_variants_serializer_data(
    values_required_for_code: list[SubFeatureValue],
    code: Code,
    scores_qs: QuerySet[CodeScore],
    substd: StandardSubcategory,
) -> list[dict]:
    variants = []
    for score in scores_qs:
        values_required_for_score = []
        for score_req in score.requirements.all():
            try:
                standardised_sf = next(
                    sf
                    for sf in score_req.sub_feature.standardised_set.all()
                    if sf.standard_id == substd.standard_key_id
                )
            except StopIteration:
                raise ValueError(
                    f"The sub-feature {score_req.sub_feature} does not have a standard representation for"
                    f" the substandard {substd}."
                )
            numeric_unit = standardised_sf.get_default_numeric_unit()
            values_required_for_score.append(
                SubFeatureValue(
                    sub_feature=score_req.sub_feature,
                    numeric_value=None,
                    numeric_range_max=score_req.numeric_option_max_breakpoint,
                    numeric_range_min=score_req.numeric_option_min_breakpoint,
                    numeric_unit=numeric_unit,
                    option=score_req.required_option,
                )
            )

        all_required_values = values_required_for_code + values_required_for_score

        description = code.build_description_from_values(substd, all_required_values)
        if not description:
            continue  # Assume we want only codes that can generate a description

        variants.append(
            {
                "display_description": description,
                "scoring": score,
                "scoring_requirements": score.requirements,
            }
        )

    return variants


class CodeScoreSerializer(serializers.ModelSerializer):
    class Meta:
        model = CodeScore
        fields = [
            "id",
            "code_id",
            "service_score",
            "structural_score",
            "required_clock_position_min",
            "required_clock_position_max",
            "required_clock_position_min_spread",
            "required_clock_position_max_spread",
            "added_service_score_per_metre",
            "added_structural_score_per_metre",
        ]


class CodeScoreRequirementSerializer(serializers.ModelSerializer):
    required_option = SubFeatureOptionSerializer(read_only=True)

    class Meta:
        model = CodeScoreRequirement
        fields = [
            "id",
            "code_score_id",
            "sub_feature_id",
            "required_option",
            "numeric_option_min_breakpoint",
            "numeric_option_max_breakpoint",
        ]


class CodeScoredVariantSerializer(serializers.Serializer):
    display_description = serializers.CharField()
    scoring = CodeScoreSerializer(read_only=True)
    scoring_requirements = CodeScoreRequirementSerializer(many=True)


class CodeVariantsSerializer(serializers.Serializer):
    code = CodeSerializer(read_only=True)
    code_requirements = CodeRequirementSerializer(many=True, read_only=True)
    variants = CodeScoredVariantSerializer(many=True, read_only=True)
