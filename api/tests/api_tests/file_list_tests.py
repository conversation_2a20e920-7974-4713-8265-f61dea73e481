from datetime import datetime, timezone

import pytest
from django.conf import settings
from rest_framework import status
from vapar.constants.processing import ProcessingStatusEnum

from api.inspections.models import FileList, ProcessingList


pytestmark = pytest.mark.django_db


@pytest.fixture
def standalone_file_record(asset_owner_org) -> FileList:
    org = asset_owner_org
    obj = FileList.objects.create(upload_org=org, target_org=org, storage_region=str(org.country.code))
    return obj


def test_uploaded_file_list(client, asset_owner_org, standard_user, inspection_settings):
    org = asset_owner_org

    f1 = FileList.objects.create(
        filename="1",
        target_org=org,
        upload_completed=True,
        upload_completed_time=datetime(2024, 1, 1, tzinfo=timezone.utc),
        processing_completed_time=datetime(2024, 1, 2, tzinfo=timezone.utc),
    )

    f2 = FileList.objects.create(
        filename="2",
        target_org=org,
        upload_completed=True,
        upload_completed_time=datetime(2024, 1, 2, tzinfo=timezone.utc),
    )
    ProcessingList.objects.create(
        associated_file=f2,
        upload_user=standard_user.full_name,
        target_org=org,
        status=ProcessingStatusEnum.PROCESSING,
    )

    f3 = FileList.objects.create(
        filename="3",
        target_org=org,
        upload_completed=True,
        upload_completed_time=datetime(2024, 1, 2, tzinfo=timezone.utc),
    )
    ProcessingList.objects.create(
        associated_file=f3,
        upload_user=standard_user.full_name,
        target_org=org,
        status=ProcessingStatusEnum.FAILED_TO_PROCESS,
    )

    client.force_login(user=standard_user)
    res = client.get(inspection_settings.list_uploaded_files_url)
    assert res.status_code == status.HTTP_200_OK
    res_data = res.json()
    assert len(res_data["results"]) == 1
    assert res_data["results"][0]["filename"] == f1.filename


def test_file_list_urls(client, asset_owner_org, standard_user, inspection_settings):
    org = asset_owner_org

    f1 = FileList.objects.create(
        filename="1.mpg",
        url="uploadedvideofiles/10_10_2024_12_34_567-1.mpg",
        play_url="uploadedvideofiles/10_10_2024_12_34_567-1_play.mpg",
        storage_region="AU",
        target_org=org,
        upload_completed=True,
        upload_completed_time=datetime(2024, 1, 1, tzinfo=timezone.utc),
        processing_completed_time=datetime(2024, 1, 2, tzinfo=timezone.utc),
    )

    f2 = FileList.objects.create(
        filename="2.mpg",
        url="uploadedvideofiles/10_10_2024_12_34_567-2.mpg",
        play_url="uploadedvideofiles/10_10_2024_12_34_567-2_play.mpg",
        storage_region="AU",
        target_org=org,
        upload_completed=True,
        upload_completed_time=datetime(2024, 1, 2, tzinfo=timezone.utc),
        processing_completed_time=datetime(2024, 1, 2, tzinfo=timezone.utc),
    )

    client.force_login(user=standard_user)
    res = client.get(inspection_settings.list_files_url)
    assert res.status_code == status.HTTP_200_OK

    res_data = res.json()
    assert len(res_data["results"]) == 2
    base_platform_url = "https://mock-url.com/"
    assert res_data["results"][1]["downloadUrl"].startswith(
        f"{base_platform_url}{f1.url}"
    ), f"downloadUrl does not start with {base_platform_url}{f1.url} ({res_data['results'][1]['downloadUrl']})"
    assert "mock-sas-token" in res_data["results"][1]["downloadUrl"]
    assert res_data["results"][1]["playUrl"].startswith(
        f"{base_platform_url}{f1.play_url}"
    ), f"playUrl does not start with {base_platform_url}{f1.play_url} ({res_data['results'][1]['playUrl']})"
    assert res_data["results"][0]["downloadUrl"].startswith(
        f"{base_platform_url}{f2.url}"
    ), f"downloadUrl does not start with {base_platform_url}{f2.url} ({res_data['results'][0]['downloadUrl']})"
    assert res_data["results"][0]["playUrl"].startswith(
        f"{base_platform_url}{f2.play_url}"
    ), f"playUrl does not start with {base_platform_url}{f2.play_url} ({res_data['results'][0]['playUrl']})"


def test_file_list_create(client, service_user, service_user_key, asset_owner_org, folder, inspection_settings):
    n_before = FileList.objects.filter(target_org=asset_owner_org).count()
    res = client.post(
        inspection_settings.list_files_url,
        data={
            "jobTree": folder.id,
        },
        HTTP_X_API_KEY=service_user_key,
        HTTP_X_TARGET_ORG_ID=asset_owner_org.id,
        content_type="application/json",
    )
    assert res.status_code == status.HTTP_201_CREATED

    n_after = FileList.objects.filter(target_org=asset_owner_org).count()
    assert n_after == n_before + 1

    data = res.json()
    file_obj = FileList.objects.get(id=data["id"])
    assert file_obj.job_tree == folder
    assert file_obj.target_org == asset_owner_org
    assert file_obj.upload_org == asset_owner_org
    assert file_obj.uploaded_by is None, "No actual file has been uploaded"
    assert file_obj.upload_completed is False
    assert file_obj.upload_completed_time is None
    assert file_obj.processing_completed_time is None
    assert file_obj.processing_started_time is None
    assert file_obj.hidden is False
    assert file_obj.total_frames == 0, "No frames have been associated with this record"

    assert data["jobTree"] == folder.id
    assert data["targetOrg"] == asset_owner_org.id
    assert data["uploadOrg"] == asset_owner_org.id
    assert data["uploadedBy"] is None
    assert data["uploadCompleted"] is False
    assert data["uploadCompletedTime"] is None
    assert data["hidden"] is False
    assert data["playUrl"] is None
    assert data["downloadUrl"] is None


def test_file_list_standalone_media_upload(
    client, service_user, service_user_key, asset_owner_org, standalone_file_record, inspection_settings
):
    res = client.post(
        inspection_settings.get_upload_standalone_url(standalone_file_record.id),
        data={
            "filename": "test.mp4",
            "fileSize": "123MB",
        },
        HTTP_X_API_KEY=service_user_key,
        HTTP_X_TARGET_ORG_ID=asset_owner_org.id,
        content_type="application/json",
    )
    assert res.status_code == status.HTTP_200_OK

    data = res.json()
    file_obj = FileList.objects.get(id=standalone_file_record.id)

    assert file_obj.filename == "test.mp4"
    assert "test.mp4" in file_obj.url
    assert settings.BLOB_STORAGE_VIDEOS_CONTAINER in file_obj.url
    assert file_obj.file_size == "123MB"

    assert "test.mp4" in data["blobUrlWithSas"]
