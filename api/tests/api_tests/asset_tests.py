import pytest
from dateutil import parser
from django.core.cache import caches
from django.test.client import Client
from rest_framework import status

from api.tests import factory
from api.defects.models import Standard, StandardHeader
from api.imports.models import Import, ImportedAsset
from api.inspections.models import Asset, Inspection, MapPointList
from api.organisations.models import AssetOwners, Contractors, Organisations
from api.tests.settings import AssetSettings, InspectionSettings
from api.users.models import CustomUser

pytestmark = [pytest.mark.django_db(databases=["default"])]
caches["inspections"].clear()


class TestAssets:
    """
    Tests for the assets API
    """

    client: Client = Client()
    product_owner: CustomUser = None
    standard_user: CustomUser = None
    asset_owner: Organisations = None
    test_asset_data: dict = None

    @pytest.fixture(autouse=True)
    def setup_method(self, product_owner, standard_user, asset_owner_org):
        self.product_owner = product_owner
        self.standard_user = standard_user
        self.client.force_login(user=self.product_owner)
        self.asset_owner = asset_owner_org
        self.test_asset_data = {
            "organisation": self.asset_owner.id,
            "type": "pipe",
            "AssetID": "TEST ASSET ID",
            "UpstreamNode": "USNODE123",
            "DownstreamNode": "DSNODE456",
            "HeightDiameter": 101,
            "Material": "VC",
            "LocationStreet": "TEST LOCATION STREET",
            "LocationTown": "TEST LOCATION TOWN",
        }

    @pytest.fixture
    def get_asset(self, asset_settings) -> dict:
        response = self.client.post(
            path=asset_settings.asset_list_url, data=self.test_asset_data, content_type="application/json"
        )

        assert response.status_code == status.HTTP_201_CREATED
        return response.json()

    def test_create_asset_as_product_owner(self, asset_settings):
        """
        Test creating an asset as product owner
        """
        response = self.client.post(
            path=asset_settings.asset_list_url, data=self.test_asset_data, content_type="application/json"
        )

        assert response.status_code == status.HTTP_201_CREATED
        response_data = response.json()

        for key, value in self.test_asset_data.items():
            assert key in response_data
            assert isinstance(response_data[key], type(value))
            assert response_data[key] == value

        asset = Asset.objects.get(uuid=response_data["uuid"])
        values = {av.standard_header.header.name: av.value for av in asset.assetvalue_set.all()}
        assert values["AssetID"] == self.test_asset_data["AssetID"]
        assert values["UpstreamNode"] == self.test_asset_data["UpstreamNode"]
        assert values["DownstreamNode"] == self.test_asset_data["DownstreamNode"]
        assert int(values["HeightDiameter"]) == self.test_asset_data["HeightDiameter"]
        assert values["Material"] == self.test_asset_data["Material"]
        assert values["LocationStreet"] == self.test_asset_data["LocationStreet"]
        assert values["LocationTown"] == self.test_asset_data["LocationTown"]
        assert values["UseOfDrainSewer"] == "SS"  # Default value

    def test_create_asset_as_standard_user(self, asset_settings):
        """
        Test creating an asset as a standard user
        """

        self.client.force_login(user=self.standard_user)
        self.test_asset_data["organisation"] = self.standard_user.organisation.id

        response = self.client.post(
            path=asset_settings.asset_list_url, data=self.test_asset_data, content_type="application/json"
        )

        assert response.status_code == status.HTTP_201_CREATED
        response_data = response.json()

        for key, value in self.test_asset_data.items():
            assert key in response_data
            assert isinstance(response_data[key], type(value))
            assert response_data[key] == value

        asset = Asset.objects.get(uuid=response_data["uuid"])
        values = {av.standard_header.header.name: av.value for av in asset.assetvalue_set.all()}
        assert values["AssetID"] == self.test_asset_data["AssetID"]
        assert values["UpstreamNode"] == self.test_asset_data["UpstreamNode"]
        assert values["DownstreamNode"] == self.test_asset_data["DownstreamNode"]
        assert int(values["HeightDiameter"]) == self.test_asset_data["HeightDiameter"]
        assert values["Material"] == self.test_asset_data["Material"]
        assert values["LocationStreet"] == self.test_asset_data["LocationStreet"]
        assert values["LocationTown"] == self.test_asset_data["LocationTown"]
        assert values["UseOfDrainSewer"] == "SS"  # Default value

    @pytest.mark.parametrize(
        "use_of_drain_sewer",
        ["SS", "SW"],
    )
    def test_create_asset_specify_use_of_drain_sewer(self, use_of_drain_sewer, asset_settings):
        """
        Test creating an asset and specifying the use of drain sewer
        """
        self.client.force_login(user=self.product_owner)
        input_data = self.test_asset_data.copy()
        input_data["UseOfDrainSewer"] = use_of_drain_sewer

        response = self.client.post(
            path=asset_settings.asset_list_url, data=input_data, content_type="application/json"
        )

        assert response.status_code == status.HTTP_201_CREATED
        response_data = response.json()
        assert response_data["UseOfDrainSewer"] == use_of_drain_sewer

        asset = Asset.objects.get(uuid=response_data["uuid"])
        created_value = asset.assetvalue_set.get(standard_header__header__name="UseOfDrainSewer")
        assert created_value.value == use_of_drain_sewer

    def test_create_asset_empty_asset_id(self, asset_settings):
        """
        Test creating an asset with an empty asset_id value
        """
        self.client.force_login(user=self.product_owner)
        self.test_asset_data["AssetID"] = ""

        response = self.client.post(
            path=asset_settings.asset_list_url, data=self.test_asset_data, content_type="application/json"
        )

        assert response.status_code == status.HTTP_201_CREATED
        response_data = response.json()

        for key, value in self.test_asset_data.items():
            assert key in response_data
            assert isinstance(response_data[key], type(value))
            assert response_data[key] == value

        asset = Asset.objects.get(uuid=response_data["uuid"])
        assert asset.assetvalue_set.all()

    def test_create_asset_duplicate_asset_id(self, asset_settings):
        """
        Test creating an asset with a duplicate asset id within an organisation.
        """
        self.client.force_login(user=self.product_owner)

        response = self.client.post(
            path=asset_settings.asset_list_url, data=self.test_asset_data, content_type="application/json"
        )
        assert response.status_code == status.HTTP_201_CREATED

        response = self.client.post(
            path=asset_settings.asset_list_url, data=self.test_asset_data, content_type="application/json"
        )
        assert response.status_code == status.HTTP_400_BAD_REQUEST

    def test_create_asset_duplicate_asset_id_different_case(self, asset_settings):
        """
        Test creating an asset with an AssetID that is a case-insensitive duplicate.
        """
        self.client.force_login(user=self.product_owner)

        response = self.client.post(
            path=asset_settings.asset_list_url, data=self.test_asset_data, content_type="application/json"
        )
        assert response.status_code == status.HTTP_201_CREATED

        self.test_asset_data["AssetID"] = self.test_asset_data["AssetID"].lower()
        response = self.client.post(
            path=asset_settings.asset_list_url, data=self.test_asset_data, content_type="application/json"
        )
        assert response.status_code == status.HTTP_400_BAD_REQUEST

    def test_get_all_assets_as_product_owner(self, asset_settings):
        """
        Test getting a list of assets as a product owner
        """
        self.client.force_login(user=self.product_owner)

        response = self.client.get(path=asset_settings.asset_list_url.format(organisation_id=self.asset_owner.id))
        assert response.status_code == status.HTTP_200_OK

    def test_get_all_assets_as_standard_user(self, asset_settings):
        """
        Test getting a list of assets as a product owner
        """
        self.client.force_login(user=self.standard_user)

        response = self.client.get(
            path=asset_settings.asset_list_url.format(organisation_id=self.standard_user.organisation.id)
        )
        assert response.status_code == status.HTTP_200_OK

    def test_search_on_asset_id(self, asset_settings, get_asset):
        self.client.force_login(user=self.standard_user)

        asset = get_asset
        assert "AssetID" in asset
        asset_id = asset["AssetID"]

        # create a second asset that should be filtered out
        second_asset_data = self.test_asset_data.copy()
        second_asset_data["AssetID"] = "different_asset_id"

        response = self.client.post(
            path=asset_settings.asset_list_url,
            data=second_asset_data,
            content_type="application/json",
        )
        assert response.status_code == status.HTTP_201_CREATED

        response = self.client.get(
            path=asset_settings.asset_list_url.format(organisation_id=self.asset_owner.id) + f"&search={asset_id}"
        )

        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()
        assert "results" in response_data
        assert isinstance(response_data["results"], list)

        assets = response_data["results"]

        for asset in assets:
            assert asset["AssetID"] == asset_id

    def test_get_all_assets_by_standard(self, asset_settings):
        """
        Test getting all assets as a standard user
        """
        self.client.force_login(user=self.standard_user)

        # create 2 assets of standard 1
        uk_v5_standard = Standard.objects.get(name="MSCC5")
        uk_v5_assets = factory.create_assets(org=self.standard_user.organisation, n=2, standard=uk_v5_standard)
        assert len(uk_v5_assets) == 2

        au_20_standard = Standard.objects.get(name="WSA-05 2020")
        au_20_assets = factory.create_assets(org=self.standard_user.organisation, n=3, standard=au_20_standard)
        assert len(au_20_assets) == 3

        # check no standard filter --> return 5 assets
        response = self.client.get(
            path=asset_settings.asset_list_url.format(organisation_id=self.standard_user.organisation.id)
        )

        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()
        assert "results" in response_data
        assert isinstance(response_data["results"], list)
        assets = response_data["results"]
        assert len(assets) == 5

        # check by standard 1 --> return 2 assets
        response = self.client.get(
            path=asset_settings.asset_list_url.format(organisation_id=self.standard_user.organisation.id)
            + f"&standard={uk_v5_standard.id}"
        )
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()
        assert "results" in response_data
        assert isinstance(response_data["results"], list)
        assets = response_data["results"]
        assert len(assets) == 2

        for asset in assets:
            assert "standard" in asset
            assert asset["standard"] is not None
            assert asset["standard"] == uk_v5_standard.id, "All assets should be of the same standard"

        # check by standard 2 --> return 3 assets
        response = self.client.get(
            path=asset_settings.asset_list_url.format(organisation_id=self.standard_user.organisation.id)
            + f"&standard={au_20_standard.id}"
        )
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()
        assert "results" in response_data
        assert isinstance(response_data["results"], list)
        assets = response_data["results"]
        assert len(assets) == 3

        for asset in assets:
            assert "standard" in asset
            assert asset["standard"] is not None
            assert asset["standard"] == au_20_standard.id, "All assets should be of the same standard"

        # check non existent standard
        non_existant_standard_id = 999
        response = self.client.get(
            path=asset_settings.asset_list_url.format(organisation_id=self.standard_user.organisation.id)
            + f"&standard={non_existant_standard_id}"
        )
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        response_data = response.json()
        assert response_data == ["Invalid standard id"]

    @pytest.mark.parametrize(
        "ordering_field",
        (
            "uuid",
            "-uuid",
            "asset_id",
            "-asset_id",
            "upstream_node",
            "-upstream_node",
            "downstream_node",
            "-downstream_node",
            "height_diameter",
            "-height_diameter",
            "material",
            "-material",
            "location_street",
            "-location_street",
            "location_town",
            "-location_town",
        ),
    )
    def test_order_asset_list(self, ordering_field: str, asset_settings):
        """
        Test to ensure the asset list can be ordered
        """
        self.client.force_login(user=self.standard_user)

        asset_values = [
            {
                "AssetID": "AAA",
                "UpstreamNode": "AA US",
                "DownstreamNode": "AA DS",
                "HeightDiameter": 100,
                "Material": "AA",
                "LocationStreet": "A Street",
                "LocationTown": "A Town",
            },
            {
                "AssetID": "BBB",
                "UpstreamNode": "BB US",
                "DownstreamNode": "BB DS",
                "HeightDiameter": 200,
                "Material": "BB",
                "LocationStreet": "B Street",
                "LocationTown": "B Town",
            },
            {
                "AssetID": "CCC",
                "UpstreamNode": "CC US",
                "DownstreamNode": "CC DS",
                "HeightDiameter": 300,
                "Material": "CC",
                "LocationStreet": "C Street",
                "LocationTown": "C Town",
            },
            {
                "AssetID": "DDD",
                "UpstreamNode": "DD US",
                "DownstreamNode": "DD DS",
                "HeightDiameter": 400,
                "Material": "DD",
                "LocationStreet": "D Street",
                "LocationTown": "D Town",
            },
            {
                "AssetID": "EEE",
                "UpstreamNode": "EE US",
                "DownstreamNode": "EE DS",
                "HeightDiameter": 500,
                "Material": "EE",
                "LocationStreet": "E Street",
                "LocationTown": "E Town",
            },
        ]

        asset_list = factory.create_assets(org=self.standard_user.organisation, n=5, asset_values_list=asset_values)

        response = self.client.get(
            path=asset_settings.asset_list_url.format(organisation_id=self.asset_owner.id)
            + f"&standard={self.standard_user.organisation.standard_key.id}&ordering={ordering_field}"
        )
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()

        assert "results" in response_data
        assert isinstance(response_data["results"], list)
        assets = response_data["results"]
        assert len(assets) == 5

        result_ids = set(result["uuid"] for result in assets)
        expected_ids = set(str(asset.uuid) for asset in asset_list)
        assert result_ids == expected_ids, "All assets should be returned"

        field_map = {
            "uuid": "uuid",
            "asset_id": "AssetID",
            "upstream_node": "UpstreamNode",
            "downstream_node": "DownstreamNode",
            "height_diameter": "HeightDiameter",
            "material": "Material",
            "location_street": "LocationStreet",
            "location_town": "LocationTown",
        }

        # now check ordering is correct
        if ordering_field.find("uuid") != -1:
            asset_values = [{"uuid": str(asset_item.uuid)} for asset_item in asset_list]

        if ordering_field.startswith("-"):
            ordering_field = ordering_field[1:]

            if ordering_field == "asset_id":
                assert [asset[field_map[ordering_field]][:2] for asset in assets] == list(
                    reversed(sorted([asset[field_map[ordering_field]][:2] for asset in asset_values]))
                )
            else:
                assert [asset[field_map[ordering_field]] for asset in assets] == list(
                    reversed(sorted([asset[field_map[ordering_field]] for asset in asset_values]))
                )
        else:
            if ordering_field == "asset_id":
                assert [asset[field_map[ordering_field]][:2] for asset in assets] == sorted(
                    [asset[field_map[ordering_field]][:2] for asset in asset_values]
                )
            else:
                assert [asset[field_map[ordering_field]] for asset in assets] == list(
                    sorted([asset[field_map[ordering_field]] for asset in asset_values])
                )

    def test_retrieve_asset(self, asset_settings):
        """
        Test retrieving a single asset
        """

        resp = self.client.post(
            path=asset_settings.asset_list_url, data=self.test_asset_data, content_type="application/json"
        )
        uid = resp.json()["uuid"]
        path = asset_settings.asset_url.format(asset_uuid=uid)
        asset = Asset.objects.filter(uuid=uid).first()
        inspections = factory.create_bulk_inspections(asset=asset, n=2)
        resp = self.client.get(path=path)
        assert resp.status_code == status.HTTP_200_OK

        resp_data = resp.json()
        assert "createdAt" in resp_data
        parser.isoparse(resp_data["createdAt"])

        del resp_data["createdAt"]  # Remove the date for comparison

        assert resp_data == {
            "uuid": uid,
            "type": self.test_asset_data["type"],
            "standard": self.asset_owner.standard_key.id,
            "organisation": self.test_asset_data["organisation"],
            "AssetID": self.test_asset_data["AssetID"],
            "UpstreamNode": self.test_asset_data["UpstreamNode"],
            "DownstreamNode": self.test_asset_data["DownstreamNode"],
            "HeightDiameter": self.test_asset_data["HeightDiameter"],
            "Material": self.test_asset_data["Material"],
            "LocationStreet": self.test_asset_data["LocationStreet"],
            "LocationTown": self.test_asset_data["LocationTown"],
            "UseOfDrainSewer": "SS",
            "YearRenewed": None,
            "extraFields": {},
            "inspectionsCount": len(inspections),
        }

    def test_retrieve_asset_not_found(self, asset_settings):
        """
        Test retrieving a single asset that does not exist
        """

        self.client.post(path=asset_settings.asset_list_url, data=self.test_asset_data, content_type="application/json")

        nonexistent_uid = "d36df87a-aad1-43d7-bad4-3da4c7cdfd32"
        path = asset_settings.asset_url.format(asset_uuid=nonexistent_uid)

        resp = self.client.get(path=path)
        assert resp.status_code == status.HTTP_404_NOT_FOUND

    def test_retrieve_asset_with_extra_values(self, asset_settings):
        resp = self.client.post(
            path=asset_settings.asset_list_url, data=self.test_asset_data, content_type="application/json"
        )
        uid = resp.json()["uuid"]

        asset = Asset.objects.get(uuid=uid)
        shs = self.standard_user.organisation.standard_key.standardheader_set.all()
        asset.assetvalue_set.create(standard_header=shs.get(header__name="NameOfSurveyor"), value="John Smith")
        asset.assetvalue_set.create(standard_header=shs.get(header__name="Weather"), value="ABC")
        asset.save()

        path = asset_settings.asset_url.format(asset_uuid=uid)
        resp = self.client.get(path=path)
        assert resp.status_code == status.HTTP_200_OK

        resp_data = resp.json()
        assert resp_data["extraFields"] == {"NameOfSurveyor": "John Smith", "Weather": "ABC"}

    def test_update_asset_change_single_value(self, asset_settings):
        resp = self.client.post(
            path=asset_settings.asset_list_url, data=self.test_asset_data, content_type="application/json"
        )
        uid = resp.json()["uuid"]
        asset = Asset.objects.get(uuid=uid)

        values_before = {val.standard_header.header.name: val.value for val in asset.assetvalue_set.all()}

        path = asset_settings.asset_url.format(asset_uuid=uid)
        new_upstream_node_value = "New US Node value"
        resp = self.client.patch(
            path=path,
            data={"UpstreamNode": new_upstream_node_value},
            content_type="application/json",
        )

        assert resp.status_code == status.HTTP_200_OK

        updated_val = asset.assetvalue_set.get(standard_header__header__name="UpstreamNode")
        assert updated_val.value == new_upstream_node_value

        values_after = {val.standard_header.header.name: val.value for val in asset.assetvalue_set.all()}
        values_after.pop("UpstreamNode")
        values_before.pop("UpstreamNode")
        assert values_before == values_after, "Other values should be unchanged"

        # check asset cache updated via asset list fetch
        response = self.client.get(path=asset_settings.asset_list_url.format(organisation_id=self.asset_owner.id))
        assert response.status_code == status.HTTP_200_OK

        response_data = response.json()
        assert "results" in response_data
        asset_list = response_data["results"]

        asset_found = False
        for asset in asset_list:
            if asset["uuid"] == uid:
                asset_found = True
                assert asset["UpstreamNode"] == new_upstream_node_value

        assert asset_found

    def test_update_asset_change_multiple(self, asset_settings):
        resp = self.client.post(
            path=asset_settings.asset_list_url, data=self.test_asset_data, content_type="application/json"
        )
        uid = resp.json()["uuid"]
        asset = Asset.objects.get(uuid=uid)
        values_before = {val.standard_header.header.name: val.value for val in asset.assetvalue_set.all()}

        path = asset_settings.asset_url.format(asset_uuid=uid)
        new_upstream_node_value = "New US Node value"
        new_downstream_node_value = "New DS Node value"
        resp = self.client.patch(
            path=path,
            data={
                "UpstreamNode": new_upstream_node_value,
                "DownstreamNode": new_downstream_node_value,
            },
            content_type="application/json",
        )

        assert resp.status_code == status.HTTP_200_OK

        updated_val = asset.assetvalue_set.get(standard_header__header__name="UpstreamNode")
        assert updated_val.value == new_upstream_node_value

        updated_val = asset.assetvalue_set.get(standard_header__header__name="DownstreamNode")
        assert updated_val.value == new_downstream_node_value

        values_after = {val.standard_header.header.name: val.value for val in asset.assetvalue_set.all()}
        values_after.pop("UpstreamNode")
        values_after.pop("DownstreamNode")
        values_before.pop("UpstreamNode")
        values_before.pop("DownstreamNode")
        assert values_before == values_after, "Other values should be unchanged"

        # check asset cache updated via asset list fetch
        response = self.client.get(path=asset_settings.asset_list_url.format(organisation_id=self.asset_owner.id))
        assert response.status_code == status.HTTP_200_OK

        response_data = response.json()
        assert "results" in response_data
        asset_list = response_data["results"]

        asset_found = False
        for asset in asset_list:
            if asset["uuid"] == uid:
                asset_found = True
                assert asset["UpstreamNode"] == new_upstream_node_value
                assert asset["DownstreamNode"] == new_downstream_node_value

        assert asset_found

    def test_asset_update_invalid_header(self, asset_settings):
        resp = self.client.post(
            path=asset_settings.asset_list_url, data=self.test_asset_data, content_type="application/json"
        )
        uid = resp.json()["uuid"]
        path = asset_settings.asset_url.format(asset_uuid=uid)
        resp = self.client.patch(
            path=path,
            data={
                "LocationStreet": "This is a valid header",
                "InvalidHeader123": "Some value...",
            },
            content_type="application/json",
        )

        assert resp.status_code == status.HTTP_400_BAD_REQUEST

    def test_update_asset_id_value_when_value_exists(self, asset_settings):
        """
        Test updating the asset id value is only allowed when empty.
        """
        self.client.force_login(user=self.product_owner)

        response = self.client.post(
            path=asset_settings.asset_list_url, data=self.test_asset_data, content_type="application/json"
        )
        assert response.status_code == status.HTTP_201_CREATED
        response_data = response.json()

        assert response_data["AssetID"] != ""

        uid = response_data["uuid"]
        path = asset_settings.asset_url.format(asset_uuid=uid)
        data = {"AssetID": "Updating asset id"}

        resp = self.client.patch(path=path, data=data, content_type="application/json")
        assert resp.status_code == status.HTTP_400_BAD_REQUEST

    def test_update_asset_id_value_when_value_empty(self, asset_settings):
        """
        Test updating the asset id value is only allowed when empty.
        """
        self.client.force_login(user=self.product_owner)
        asset_data = self.test_asset_data.copy()
        asset_data["AssetID"] = ""

        response = self.client.post(
            path=asset_settings.asset_list_url, data=asset_data, content_type="application/json"
        )
        assert response.status_code == status.HTTP_201_CREATED
        response_data = response.json()

        assert response_data["AssetID"] == ""

        uid = response_data["uuid"]
        path = asset_settings.asset_url.format(asset_uuid=uid)
        new_asset_id_value = "Updating asset id"
        data = {"AssetID": new_asset_id_value}

        resp = self.client.patch(path=path, data=data, content_type="application/json")
        assert resp.status_code == status.HTTP_200_OK

        # check asset cache updated via asset list fetch
        response = self.client.get(path=asset_settings.asset_list_url.format(organisation_id=self.asset_owner.id))
        assert response.status_code == status.HTTP_200_OK

        response_data = response.json()
        assert "results" in response_data
        asset_list = response_data["results"]

        asset_found = False
        for asset in asset_list:
            if asset["uuid"] == uid:
                asset_found = True
                assert asset["AssetID"] == new_asset_id_value

        assert asset_found

    def test_update_asset_with_multiple_linked_inspections(
        self, asset_settings: AssetSettings, inspection_settings: InspectionSettings
    ):
        """
        Testing that fetching the inspection after updating an asset value updates the inspection.
        """
        self.client.force_login(user=self.standard_user)

        asset = factory.create_assets(org=self.standard_user.organisation)[0]
        inspections = factory.create_bulk_inspections(asset=asset, n=2)
        assert len(inspections) == 2

        # fetch the inspection list
        response = self.client.get(
            path=inspection_settings.inspection_list_url.format(organisation_id=self.asset_owner.id)
        )

        assert response.status_code == status.HTTP_200_OK
        response_data = response.data
        assert "results" in response_data
        inspection_list = response_data["results"]
        assert len(inspection_list) == 2

        for inspection in inspection_list:
            assert "asset" in inspection
            assert inspection["asset"]["uuid"] == asset.uuid

        # update the asset
        update_upstream_node = {"UpstreamNode": "new_upstream_node"}

        response = self.client.patch(
            path=asset_settings.asset_url.format(asset_uuid=asset.uuid),
            data=update_upstream_node,
            content_type="application/json",
        )

        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()
        assert "UpstreamNode" in response_data
        assert response_data["UpstreamNode"] == update_upstream_node["UpstreamNode"]

        # fetch inspection list
        response = self.client.get(
            path=inspection_settings.inspection_list_url.format(organisation_id=self.asset_owner.id)
        )

        assert response.status_code == status.HTTP_200_OK
        response_data = response.data
        assert "results" in response_data
        inspection_list = response_data["results"]
        assert len(inspection_list) == 2

        # check results show updated values
        for inspection in inspection_list:
            assert "asset" in inspection
            assert inspection["asset"]["uuid"] == asset.uuid
            assert inspection["asset"]["upstream_node"] == update_upstream_node["UpstreamNode"]

            # now check mpl has synced
            mappoint = MapPointList.objects.get(inspection_id=inspection["inspection_id"])
            assert mappoint is not None
            assert mappoint.upstream_node == update_upstream_node["UpstreamNode"]

    def test_update_asset_missing_asset_value_record(self, asset_settings: AssetSettings):
        """
        Test that on asset update, an asset value will be created if it does not exist.
        """
        self.client.force_login(user=self.standard_user)

        asset = factory.create_assets(
            org=self.standard_user.organisation,
            asset_values_list=[
                {
                    "AssetID": "TEST ASSET ID",
                    "UpstreamNode": "USNODE123",
                    "DownstreamNode": "DSNODE456",
                    "HeightDiameter": 101,
                    "Material": "VC",
                    "LocationStreet": "TEST LOCATION STREET",
                    "LocationTown": "TEST LOCATION TOWN",
                }
            ],
        )[0]

        # delete an asset value record
        asset.assetvalue_set.all()[0].delete()
        assert asset.assetvalue_set.count() == 6

        # update the asset with all values (1 will be new)
        data = {
            "AssetID": "TEST ASSET ID",
            "UpstreamNode": "USNODE123",
            "DownstreamNode": "DSNODE456",
            "HeightDiameter": 101,
            "Material": "VC",
            "LocationStreet": "TEST LOCATION STREET",
            "LocationTown": "TEST LOCATION TOWN",
        }

        response = self.client.patch(
            path=asset_settings.asset_url.format(asset_uuid=asset.uuid),
            data=data,
            content_type="application/json",
        )

        assert response.status_code == status.HTTP_200_OK

        for key, value in data.items():
            assert key in response.data
            assert response.data[key] == value

        # check db
        asset = Asset.objects.get(uuid=asset.uuid)
        assert asset.assetvalue_set.count() == 7
        # check standards match
        assert len({av.standard_header.standard for av in asset.assetvalue_set.all()}) == 1

    def test_get_asset_linked_inspections(self, asset_settings: AssetSettings, get_asset: dict):
        """
        Testing getting the inspections for a given asset
        """
        self.client.force_login(user=self.standard_user)
        asset = get_asset
        assert "uuid" in asset.keys()
        asset_uuid = asset["uuid"]

        response = self.client.get(
            path=asset_settings.asset_inspections_url.format(asset_uuid=asset_uuid),
            content_type="application/json",
        )

        assert response.status_code == status.HTTP_200_OK
        assert "count" in response.data
        assert response.data["count"] == 0

        factory.create_bulk_inspections(asset=Asset.objects.get(uuid=asset_uuid), n=1)

        response = self.client.get(
            path=asset_settings.asset_inspections_url.format(asset_uuid=asset_uuid),
            content_type="application/json",
        )

        assert response.status_code == status.HTTP_200_OK
        assert "count" in response.data
        assert response.data["count"] == 1

        assert "results" in response.data
        inspections = response.data["results"]
        assert isinstance(inspections, list)

        assert "asset" in inspections[0]
        assert str(inspections[0]["asset"]["uuid"]) == asset_uuid

    def test_get_asset_linked_inspections_with_hidden_files(self, asset_settings: AssetSettings, get_asset: dict):
        """
        Test that inspections with hidden files are not returned as linked inspections
        """
        self.client.force_login(user=self.standard_user)
        asset = get_asset
        assert "uuid" in asset.keys()
        asset_uuid = asset["uuid"]

        inspection = factory.create_bulk_inspections(asset=Asset.objects.get(uuid=asset_uuid), n=1)[0]

        response = self.client.get(
            path=asset_settings.asset_inspections_url.format(asset_uuid=asset_uuid),
            content_type="application/json",
        )

        assert response.status_code == status.HTTP_200_OK
        response_data = response.data
        assert "count" in response_data
        assert response_data["count"] == 1

        assert "results" in response_data
        inspections = response_data["results"]
        assert isinstance(inspections, list)

        for insp in inspections:
            assert "file" in insp
            assert insp["file"]
            assert insp["file"]["hidden"] is False

        inspection = Inspection.objects.filter(uuid=inspection["inspection_id"]).first()
        setattr(inspection.file, "hidden", True)
        inspection.file.save()

        response = self.client.get(
            path=asset_settings.asset_inspections_url.format(asset_uuid=asset_uuid),
            content_type="application/json",
        )

        assert response.status_code == status.HTTP_200_OK
        response_data = response.data
        assert "count" in response_data
        assert response_data["count"] == 0

    def test_get_asset_linked_inspections_with_unrelated_contractor(
        self,
        asset_settings: AssetSettings,
        get_asset: Asset,
        linked_contractor_user: CustomUser,
    ):
        self.client.force_login(user=linked_contractor_user)
        asset = get_asset
        asset_uuid = asset["uuid"]

        factory.create_bulk_inspections(asset=Asset.objects.get(uuid=asset_uuid))

        response = self.client.get(
            path=asset_settings.asset_inspections_url.format(asset_uuid=asset_uuid),
            content_type="application/json",
        )

        assert response.status_code == status.HTTP_200_OK
        response_data = response.data
        assert response_data["count"] == 0
        assert len(response_data["results"]) == 0, "Inspection not created by contractor isn't visible to them"

    def test_delete_asset_as_product_owner(self, get_asset, asset_settings):
        """
        Test an asset owner can delete their own asset
        """
        self.client.force_login(user=self.product_owner)
        asset = get_asset
        asset_uuid = asset["uuid"]

        response = self.client.delete(path=asset_settings.asset_url.format(asset_uuid=asset["uuid"]))
        assert response.status_code == status.HTTP_204_NO_CONTENT

        asset = Asset.objects.filter(uuid=asset_uuid).first()
        assert asset is None

        # check asset cache updated via asset list fetch
        response = self.client.get(path=asset_settings.asset_list_url.format(organisation_id=self.asset_owner.id))
        assert response.status_code == status.HTTP_200_OK

        response_data = response.json()
        assert "results" in response_data
        asset_list = response_data["results"]

        asset_found = False
        for item in asset_list:
            if item["uuid"] == asset_uuid:
                asset_found = True

        assert not asset_found

    def test_delete_asset_as_asset_owner(self, get_asset, asset_settings):
        """
        Test an asset owner can delete their own asset
        """
        user = self.standard_user
        assert user.is_asset_owner()

        self.client.force_login(user=user)

        asset = get_asset
        asset_uuid = asset["uuid"]

        response = self.client.delete(path=asset_settings.asset_url.format(asset_uuid=asset["uuid"]))
        assert response.status_code == status.HTTP_204_NO_CONTENT

        asset = Asset.objects.filter(uuid=asset_uuid).first()
        assert asset is None

        # check asset cache updated via asset list fetch
        response = self.client.get(path=asset_settings.asset_list_url.format(organisation_id=self.asset_owner.id))
        assert response.status_code == status.HTTP_200_OK

        response_data = response.json()
        assert "results" in response_data
        asset_list = response_data["results"]

        asset_found = False
        for item in asset_list:
            if item["uuid"] == asset_uuid:
                asset_found = True

        assert not asset_found

    def test_delete_asset_asset_contractor(self, linked_contractor_user, get_asset, asset_settings):
        """
        Test a contractor with access to an asset cannot delete and asset
        """
        user = linked_contractor_user
        assert not user.is_asset_owner()

        self.client.force_login(user=user)

        asset = get_asset

        response = self.client.delete(path=asset_settings.asset_url.format(asset_uuid=asset["uuid"]))
        assert response.status_code == status.HTTP_403_FORBIDDEN
        response_data = response.json()
        assert "detail" in response_data
        assert response_data["detail"] == "You do not have permission to delete this asset"

        asset = Asset.objects.filter(uuid=asset["uuid"]).first()
        assert asset is not None

    def test_delete_asset_with_linked_inspections(self, get_asset, asset_settings):
        """
        Test an asset cannot be deleted if inspections are linked to it
        """
        self.client.login(user=self.standard_user)

        asset = Asset.objects.filter(uuid=get_asset["uuid"]).first()
        inspections = factory.create_bulk_inspections(asset=asset, n=1)
        assert inspections is not None

        inspection = inspections[0]
        assert "asset" in inspection
        assert inspection["asset"]["uuid"] == asset.uuid
        assert asset.inspection_set.all() is not None

        response = self.client.delete(path=asset_settings.asset_url.format(asset_uuid=asset.uuid))
        assert response.status_code == status.HTTP_403_FORBIDDEN
        response_data = response.json()
        assert "detail" in response_data
        assert response_data["detail"] == "You are unable to delete assets with linked inspections"

        asset = Asset.objects.filter(uuid=asset.uuid).first()
        assert asset is not None

    def test_update_asset_value(self, asset_settings):
        """
        Test updating an asset value
        """
        self.client.force_login(user=self.standard_user)

        asset = factory.create_assets(org=self.standard_user.organisation)[0]
        asset_values = asset.assetvalue_set.all()

        material_asset_value = asset_values.filter(standard_header__header__name="Material").first()
        assert material_asset_value is not None

        failing_value = "Not a listed material"
        response = self.client.patch(
            path=asset_settings.asset_value_url.format(asset_value_uuid=material_asset_value.uuid),
            data={"value": failing_value},
            content_type="application/json",
        )
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        response_data = response.json()
        assert isinstance(response_data, list)
        assert len(response_data) == 1
        assert "message" in response_data[0]
        assert (
            response_data[0]["message"]
            == "Not a listed material is an invalid value for this field. Expected data type is one of the following: "
            "AC, BL, BR, CI, CL, CO, CS, DI, EP, FC, FRP, GI, MAC, MAR, MX, PVC, PE, PF, PP, PS, RC, SPC, ST, VC, "
            "X, XI, XP, Z"
        )

        passing_value = "VC"
        response = self.client.patch(
            path=asset_settings.asset_value_url.format(asset_value_uuid=material_asset_value.uuid),
            data={"value": passing_value},
            content_type="application/json",
        )
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()
        assert "value" in response_data
        assert response_data["value"] == passing_value

        # check the value has been updated
        updated_value = asset.assetvalue_set.get(uuid=material_asset_value.uuid)
        assert updated_value.value == passing_value

    def test_create_asset_as_contractor_bad_org(self, client, linked_contractor_user, asset_settings, asset_owner_org):
        user = linked_contractor_user
        assert not user.is_asset_owner()

        asset_owner = AssetOwners.objects.filter(org=asset_owner_org).first()
        asset_owner.contractor.remove(Contractors.objects.get(org=user.organisation))

        client.force_login(user=user)

        res = client.post(
            path=asset_settings.asset_list_url, data=self.test_asset_data, content_type="application/json"
        )
        assert res.status_code == status.HTTP_403_FORBIDDEN

        test_data = self.test_asset_data.copy()
        test_data["organisation"] = user.organisation.id
        res = client.post(path=asset_settings.asset_list_url, data=test_data, content_type="application/json")
        # Contractors allowed to create Assets to themselves (currently)
        assert res.status_code == status.HTTP_201_CREATED

    def test_asset_list_bulk_delete(self, asset_settings):
        """
        Test successfully deleting a list of assets
        """
        self.client.force_login(user=self.standard_user)

        assets = factory.create_assets(org=self.asset_owner, n=2)
        asset_uuids = [asset.uuid for asset in assets]

        response = self.client.delete(
            path=asset_settings.asset_list_url.format(organisation_id=self.asset_owner.id),
            data={"asset_uuids": asset_uuids},
            content_type="application/json",
        )

        assert response.status_code == status.HTTP_204_NO_CONTENT

        for asset_uuid in asset_uuids:
            assert not Asset.objects.filter(uuid=asset_uuid).exists()

        # Verify cache updated via asset list fetch
        response = self.client.get(path=asset_settings.asset_list_url.format(organisation_id=self.asset_owner.id))
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()
        assert "results" in response_data
        remaining_assets = response_data["results"]

        for asset_uuid in asset_uuids:
            assert not any(asset["uuid"] == asset_uuid for asset in remaining_assets)

    def test_bulk_delete_assets_with_linked_inspections(self, asset_settings):
        """
        Test failing to delete an asset with linked inspections
        """
        self.client.force_login(user=self.standard_user)

        # Create asset and link an inspection to it
        asset = factory.create_assets(org=self.asset_owner)[0]
        factory.create_bulk_inspections(asset=asset, n=1)[0]

        # Prepare asset UUID for deletion
        asset_uuid = asset.uuid

        response = self.client.delete(
            path=asset_settings.asset_list_url.format(organisation_id=self.asset_owner.id),
            data={"asset_uuids": [asset_uuid]},
            content_type="application/json",
        )

        assert response.status_code == status.HTTP_403_FORBIDDEN
        response_data = response.json()
        assert "detail" in response_data
        assert response_data["detail"] == f"Asset {asset_uuid} has linked inspections and cannot be deleted"

        # Ensure the asset is still present
        assert Asset.objects.filter(uuid=asset_uuid).exists()

    def test_bulk_asset_put(self, asset_settings, standard_user):
        """
        Test that given details of multiple assets (existing and non-existing), we correctly update/create these
        """
        org: Organisations = self.asset_owner
        self.client.force_login(user=self.standard_user)

        asset = factory.create_assets(org=org)[0]
        asset_id = asset.assetvalue_set.get(standard_header__header__name="AssetID").value
        import_obj = Import.objects.create(target_org=org, created_by=standard_user, type="AS", payload={})

        data = [
            {
                "standardID": org.standard_key.id,
                "AssetID": asset_id,
                "UpstreamNode": "USNODE123",
                "importedFromID": import_obj.id,
            },
            {
                "standardID": org.standard_key.id,
                "AssetID": "New Asset ID",
                "UpstreamNode": "USNODE456",
                "importedFromID": import_obj.id,
            },
        ]

        response = self.client.put(
            path=asset_settings.asset_list_url.format(organisation_id=self.asset_owner.id),
            data=data,
            content_type="application/json",
        )

        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()

        assert response_data[0]["standard"] == org.standard_key.id
        assert response_data[0]["asset_id"] == asset_id
        assert response_data[0]["upstream_node"] == "USNODE123"

        assert response_data[1]["standard"] == org.standard_key.id
        assert response_data[1]["asset_id"] == "New Asset ID"
        assert response_data[1]["upstream_node"] == "USNODE456"

        updated_asset_values = (
            Asset.objects.filter(uuid=response_data[0]["uuid"]).first().get_asset_values(get_value_dict=True)
        )
        created_asset_values = (
            Asset.objects.filter(uuid=response_data[1]["uuid"]).first().get_asset_values(get_value_dict=True)
        )

        assert updated_asset_values["Upstream Node"] == "USNODE123"
        assert created_asset_values["Upstream Node"] == "USNODE456"

        updated_asset_ia = ImportedAsset.objects.filter(asset__uuid=response_data[0]["uuid"]).first()
        created_asset_ia = ImportedAsset.objects.filter(asset__uuid=response_data[1]["uuid"]).first()

        assert updated_asset_ia.import_obj == import_obj
        assert created_asset_ia.import_obj == import_obj


class TestMatchOrCreateAssets:
    def test_no_match(self, client, asset_settings, service_user, service_user_key, asset_owner_org):
        org: Organisations = asset_owner_org

        n_assets_before = Asset.objects.count()

        res = client.post(
            path=asset_settings.asset_match_or_create_url,
            HTTP_X_API_KEY=service_user_key,
            HTTP_X_TARGET_ORG_ID=org.id,
            content_type="application/json",
            data={
                "standardID": org.standard_key.id,
                "UseOfDrainSewer": "SS",
                "AssetID": None,
                "UpstreamNode": "USNODE123",
                "DownstreamNode": "DSNODE456",
                "HeightDiameter": 101,
                "Material": "VC",
                "LocationStreet": "TEST LOCATION STREET",
                "LocationTown": "TEST LOCATION TOWN",
            },
        )

        assert res.status_code == status.HTTP_201_CREATED

        data = res.json()

        assert data["isMatched"] is False
        assert data["asset"]["type"] == "pipe"
        assert data["asset"]["UseOfDrainSewer"] == "SS"
        assert data["asset"]["standard"] == org.standard_key.id
        assert data["asset"]["organisation"] == org.id
        assert data["asset"]["UpstreamNode"] == "USNODE123"
        assert data["asset"]["DownstreamNode"] == "DSNODE456"
        assert data["asset"]["Material"] == "VC"
        assert data["asset"]["LocationStreet"] == "TEST LOCATION STREET"
        assert data["asset"]["LocationTown"] == "TEST LOCATION TOWN"
        assert data["asset"]["HeightDiameter"] == 101
        assert data["asset"]["AssetID"] == ""

        asset = Asset.objects.get(uuid=data["asset"]["uuid"])
        assert asset.organisation == org

        vals = {v.standard_header.header.name: v.value for v in asset.assetvalue_set.all()}
        assert vals["UpstreamNode"] == "USNODE123"
        assert vals["DownstreamNode"] == "DSNODE456"
        assert vals["Material"] == "VC"
        assert vals["LocationStreet"] == "TEST LOCATION STREET"
        assert vals["LocationTown"] == "TEST LOCATION TOWN"
        assert vals["HeightDiameter"] == "101"
        assert vals["AssetID"] == ""
        assert vals["UseOfDrainSewer"] == "SS"

        assert Asset.objects.count() == n_assets_before + 1

    def test_match_on_asset_id(self, client, asset_settings, service_user, service_user_key, asset_owner_org):
        org: Organisations = asset_owner_org

        asset = factory.create_assets(org=org)[0]
        asset_vals = {v.standard_header.header.name: v.value for v in asset.assetvalue_set.all()}

        n_assets_before = Asset.objects.count()

        res = client.post(
            path=asset_settings.asset_match_or_create_url,
            HTTP_X_API_KEY=service_user_key,
            HTTP_X_TARGET_ORG_ID=org.id,
            content_type="application/json",
            data={
                "standardID": org.standard_key.id,
                "AssetID": asset.assetvalue_set.get(standard_header__header__name="AssetID").value,
                "UpstreamNode": "USNODE123",
            },
        )

        assert res.status_code == status.HTTP_200_OK

        data = res.json()

        assert data["isMatched"] is True
        assert data["asset"]["uuid"] == str(asset.uuid)
        assert data["asset"]["type"] == "pipe"
        assert data["asset"]["standard"] == org.standard_key.id
        assert data["asset"]["organisation"] == org.id
        assert data["asset"]["UpstreamNode"] == asset_vals["UpstreamNode"]
        assert data["asset"]["DownstreamNode"] == asset_vals["DownstreamNode"]
        assert data["asset"]["Material"] == asset_vals["Material"]
        assert data["asset"]["LocationStreet"] == asset_vals["LocationStreet"]
        assert data["asset"]["LocationTown"] == asset_vals["LocationTown"]
        assert data["asset"]["HeightDiameter"] == int(asset_vals["HeightDiameter"])

        assert Asset.objects.count() == n_assets_before

    def test_match_on_asset_id_case_insensitive(
        self, client, asset_settings, service_user, service_user_key, asset_owner_org
    ):
        org: Organisations = asset_owner_org

        asset = factory.create_assets(org=org)[0]
        asset_id = asset.assetvalue_set.get(standard_header__header__name="AssetID")
        asset_id.value = asset_id.value.lower()
        asset_id.save()
        asset_vals = {v.standard_header.header.name: v.value for v in asset.assetvalue_set.all()}

        n_assets_before = Asset.objects.count()
        different_case_id = asset_id.value.upper()
        res = client.post(
            path=asset_settings.asset_match_or_create_url,
            HTTP_X_API_KEY=service_user_key,
            HTTP_X_TARGET_ORG_ID=org.id,
            content_type="application/json",
            data={
                "standardID": org.standard_key.id,
                "AssetID": different_case_id,
                "UpstreamNode": "USNODE123",
            },
        )

        assert res.status_code == status.HTTP_200_OK

        data = res.json()

        assert data["isMatched"] is True
        assert data["asset"]["uuid"] == str(asset.uuid)
        assert data["asset"]["type"] == "pipe"
        assert data["asset"]["standard"] == org.standard_key.id
        assert data["asset"]["organisation"] == org.id
        assert data["asset"]["UpstreamNode"] == asset_vals["UpstreamNode"]
        assert data["asset"]["DownstreamNode"] == asset_vals["DownstreamNode"]
        assert data["asset"]["Material"] == asset_vals["Material"]
        assert data["asset"]["LocationStreet"] == asset_vals["LocationStreet"]
        assert data["asset"]["LocationTown"] == asset_vals["LocationTown"]
        assert data["asset"]["HeightDiameter"] == int(asset_vals["HeightDiameter"])
        assert data["asset"]["AssetID"] == asset_id.value, "Should use the original case of the asset id"

        assert Asset.objects.count() == n_assets_before

    def test_match_on_fields(self, client, asset_settings, service_user, service_user_key, asset_owner_org):
        org: Organisations = asset_owner_org

        asset = factory.create_assets(org=org)[0]
        asset_vals = {v.standard_header.header.name: v.value for v in asset.assetvalue_set.all()}

        n_assets_before = Asset.objects.count()

        res = client.post(
            path=asset_settings.asset_match_or_create_url,
            HTTP_X_API_KEY=service_user_key,
            HTTP_X_TARGET_ORG_ID=org.id,
            content_type="application/json",
            data={
                "standardID": org.standard_key.id,
                "UpstreamNode": asset.assetvalue_set.get(standard_header__header__name="UpstreamNode").value,
                "DownstreamNode": asset.assetvalue_set.get(standard_header__header__name="DownstreamNode").value,
                "HeightDiameter": 225,
                "Material": "VC",
                "LocationStreet": asset.assetvalue_set.get(standard_header__header__name="LocationStreet").value,
                "LocationTown": asset.assetvalue_set.get(standard_header__header__name="LocationTown").value,
            },
        )

        assert res.status_code == status.HTTP_200_OK

        data = res.json()

        assert data["isMatched"] is True
        assert data["asset"]["uuid"] == str(asset.uuid)
        assert data["asset"]["type"] == "pipe"
        assert data["asset"]["standard"] == org.standard_key.id
        assert data["asset"]["organisation"] == org.id
        assert data["asset"]["UpstreamNode"] == asset_vals["UpstreamNode"]
        assert data["asset"]["DownstreamNode"] == asset_vals["DownstreamNode"]
        assert data["asset"]["Material"] == asset_vals["Material"]
        assert data["asset"]["LocationStreet"] == asset_vals["LocationStreet"]
        assert data["asset"]["LocationTown"] == asset_vals["LocationTown"]
        assert data["asset"]["HeightDiameter"] == int(asset_vals["HeightDiameter"])

        assert Asset.objects.count() == n_assets_before

    def test_has_fields_but_no_match(self, client, asset_settings, service_user, service_user_key, asset_owner_org):
        org: Organisations = asset_owner_org

        factory.create_assets(org=org, n=10)

        n_assets_before = Asset.objects.count()

        res = client.post(
            path=asset_settings.asset_match_or_create_url,
            HTTP_X_API_KEY=service_user_key,
            HTTP_X_TARGET_ORG_ID=org.id,
            content_type="application/json",
            data={
                "standardID": org.standard_key.id,
                "UpstreamNode": "Some unique US node...",
                "DownstreamNode": "DSNODE456",
                "HeightDiameter": 101,
                "Material": "VC",
                "LocationStreet": "TEST LOCATION STREET",
                "LocationTown": "TEST LOCATION TOWN",
            },
        )

        assert res.status_code == status.HTTP_201_CREATED

        data = res.json()

        assert data["isMatched"] is False
        assert data["asset"]["type"] == "pipe"
        assert data["asset"]["standard"] == org.standard_key.id
        assert data["asset"]["organisation"] == org.id
        assert data["asset"]["UpstreamNode"] == "Some unique US node..."
        assert data["asset"]["DownstreamNode"] == "DSNODE456"
        assert data["asset"]["Material"] == "VC"
        assert data["asset"]["LocationStreet"] == "TEST LOCATION STREET"
        assert data["asset"]["LocationTown"] == "TEST LOCATION TOWN"
        assert data["asset"]["HeightDiameter"] == 101
        assert data["asset"]["AssetID"] == ""

        asset = Asset.objects.get(uuid=data["asset"]["uuid"])

        vals = {v.standard_header.header.name: v.value for v in asset.assetvalue_set.all()}
        assert vals["UpstreamNode"] == "Some unique US node..."
        assert vals["DownstreamNode"] == "DSNODE456"
        assert vals["Material"] == "VC"
        assert vals["LocationStreet"] == "TEST LOCATION STREET"
        assert vals["LocationTown"] == "TEST LOCATION TOWN"
        assert vals["HeightDiameter"] == "101"
        assert vals["AssetID"] == ""

        assert Asset.objects.count() == n_assets_before + 1

    def test_contractor_user_create(self, client, linked_contractor_user, asset_settings, service_user_key):
        user = linked_contractor_user
        assert not user.is_asset_owner()

        res = client.post(
            path=asset_settings.asset_match_or_create_url,
            content_type="application/json",
            HTTP_X_API_KEY=service_user_key,
            HTTP_X_TARGET_ORG_ID=linked_contractor_user.organisation.id,
            data={
                "orgID": user.organisation.id,
                "standardID": user.organisation.standard_key.id,
                "UpstreamNode": "USNODE1",
                "DownstreamNode": "DSNODE2",
                "HeightDiameter": 100,
                "Material": "VC",
                "LocationStreet": "TEST LOCATION STREET",
                "LocationTown": "TEST LOCATION TOWN",
            },
        )

        assert res.status_code == status.HTTP_201_CREATED

    def test_contractor_user_create_with_org(
        self, client, linked_contractor_user, asset_settings, asset_owner_org, service_user_key
    ):
        user = linked_contractor_user
        assert not user.is_asset_owner()

        res = client.post(
            path=asset_settings.asset_match_or_create_url,
            content_type="application/json",
            HTTP_X_API_KEY=service_user_key,
            HTTP_X_TARGET_ORG_ID=linked_contractor_user.organisation.id,
            data={
                "standardID": user.organisation.standard_key.id,
                "orgID": asset_owner_org.id,
                "UpstreamNode": "USNODE1",
                "DownstreamNode": "DSNODE2",
                "HeightDiameter": 100,
                "Material": "VC",
                "LocationStreet": "TEST LOCATION STREET",
                "LocationTown": "TEST LOCATION TOWN",
            },
        )

        assert res.status_code == status.HTTP_201_CREATED

    def test_match_with_existing_in_same_standard(
        self, client, asset_settings, service_user, service_user_key, asset_owner_org
    ):
        org = asset_owner_org

        asset = factory.create_assets(org=org)[0]

        standard = Standard.objects.get(name="PACP7")

        n_assets_before = Asset.objects.count()

        insp_data = factory.create_bulk_inspections(asset=asset)[0]
        insp_obj = Inspection.objects.get(uuid=insp_data["inspection_id"])
        insp_obj.folder.standard_key = standard
        insp_obj.folder.save()

        res = client.post(
            path=asset_settings.asset_match_or_create_url,
            HTTP_X_API_KEY=service_user_key,
            HTTP_X_TARGET_ORG_ID=org.id,
            content_type="application/json",
            data={
                "standardID": standard.id,
                "AssetID": asset.assetvalue_set.get(standard_header__header__name="AssetID").value,
            },
        )

        assert res.status_code == status.HTTP_200_OK
        assert Asset.objects.count() == n_assets_before

    def test_doesnt_match_with_existing_that_has_different_standard(
        self, client, asset_settings, service_user, service_user_key, asset_owner_org
    ):
        org = asset_owner_org

        asset = factory.create_assets(org=org)[0]

        standard = Standard.objects.get(name="PACP7")  # Different to the org's default standard

        n_assets_before = Asset.objects.count()

        insp_data = factory.create_bulk_inspections(asset=asset)[0]
        insp_obj = Inspection.objects.get(uuid=insp_data["inspection_id"])
        insp_obj.folder.standard_key = standard
        insp_obj.folder.save()

        res = client.post(
            path=asset_settings.asset_match_or_create_url,
            HTTP_X_API_KEY=service_user_key,
            HTTP_X_TARGET_ORG_ID=org.id,
            content_type="application/json",
            data={
                "standardID": org.standard_key.id,
                "AssetID": asset.assetvalue_set.get(standard_header__header__name="AssetID").value,
            },
        )

        assert res.status_code == status.HTTP_400_BAD_REQUEST, "Can't create a new asset with the same AssetID"
        assert Asset.objects.count() == n_assets_before


class TestAssetFiltering:
    @pytest.fixture
    def assets(self, asset_owner_org: Organisations, standard_user: CustomUser) -> list[Asset]:
        org = asset_owner_org

        standard = org.standard_key
        sh_us_node = StandardHeader.objects.get(header__name="UpstreamNode", standard=standard, header__type="asset")
        sh_ds_node = StandardHeader.objects.get(header__name="DownstreamNode", standard=standard, header__type="asset")
        sh_town = StandardHeader.objects.get(header__name="LocationTown", standard=standard, header__type="asset")
        sh_treet = StandardHeader.objects.get(header__name="LocationStreet", standard=standard, header__type="asset")
        sh_material = StandardHeader.objects.get(header__name="Material", standard=standard, header__type="asset")
        sh_diameter = StandardHeader.objects.get(header__name="HeightDiameter", standard=standard, header__type="asset")
        sh_asset_id = StandardHeader.objects.get(header__name="AssetID", standard=standard, header__type="asset")

        a1 = Asset.objects.create(organisation=org)
        a1.assetvalue_set.create(standard_header=sh_us_node, value="A1 Upstream")
        a1.assetvalue_set.create(standard_header=sh_ds_node, value="A1 Downstream")
        a1.assetvalue_set.create(standard_header=sh_town, value="Shared Town")
        a1.assetvalue_set.create(standard_header=sh_treet, value="Street1")
        a1.assetvalue_set.create(standard_header=sh_material, value="Vitrified Clay")
        a1.assetvalue_set.create(standard_header=sh_diameter, value="60")
        a1.assetvalue_set.create(standard_header=sh_asset_id, value="AssetID 1")

        a2 = Asset.objects.create(organisation=org)
        a2.assetvalue_set.create(standard_header=sh_us_node, value="A2 Upstream")
        a2.assetvalue_set.create(standard_header=sh_ds_node, value="A2 Downstream")
        a2.assetvalue_set.create(standard_header=sh_town, value="Shared Town")
        a2.assetvalue_set.create(standard_header=sh_treet, value="Street2")
        a2.assetvalue_set.create(standard_header=sh_material, value="Concrete")
        a2.assetvalue_set.create(standard_header=sh_diameter, value="100")
        a2.assetvalue_set.create(standard_header=sh_asset_id, value="AssetID 2")

        a3 = Asset.objects.create(organisation=org)
        a3.assetvalue_set.create(standard_header=sh_us_node, value="A3 Upstream")
        a3.assetvalue_set.create(standard_header=sh_ds_node, value="A3 Downstream")
        a3.assetvalue_set.create(standard_header=sh_town, value="Town3")
        a3.assetvalue_set.create(standard_header=sh_treet, value="Street3")
        a3.assetvalue_set.create(standard_header=sh_material, value="Concrete")
        a3.assetvalue_set.create(standard_header=sh_diameter, value="30")
        a3.assetvalue_set.create(standard_header=sh_asset_id, value="AssetID 3")

        return [a1, a2, a3]

    @pytest.mark.parametrize(
        ("case_name", "search_term", "expected_asset_idxs"),
        [
            ("Unfiltered", "", {0, 1, 2}),
            ("Matches US node", "A1 Upstream", {0}),
            ("Matches DS node", "A2 Downstream", {1}),
            ("Matches multiple towns", "Shared To", {0, 1}),
            ("None matched", "xyz", set()),
            ("Matches Asset ID", "ID 3", {2}),
            ("Matches multiple materials", "conc", {1, 2}),
        ],
    )
    def test_searching(
        self,
        case_name: str,
        search_term: str,
        expected_asset_idxs: set[int],
        assets,
        client,
        asset_settings,
        asset_owner_org,
        standard_user,
    ):
        org = asset_owner_org

        client.force_login(user=standard_user)
        res = client.get(
            asset_settings.asset_list_url.format(organisation_id=org.id)
            + f"&standard={org.standard_key.id}&search={search_term}"
        )
        assert res.status_code == status.HTTP_200_OK
        data = res.json()

        actual_asset_ids = {a["uuid"] for a in data["results"]}
        actual_idxs = {i for i, a in enumerate(assets) if str(a.uuid) in actual_asset_ids}
        assert actual_idxs == expected_asset_idxs, f"Failed for case: {case_name}"
        assert data["count"] == len(expected_asset_idxs)

    @pytest.mark.parametrize(
        ("ordering", "expected_order"),
        [
            ("", [2, 1, 0]),  # Descending by created_at
            ("upstream_node", [0, 1, 2]),
            ("-upstream_node", [2, 1, 0]),
            ("downstream_node", [0, 1, 2]),
            ("-downstream_node", [2, 1, 0]),
            ("height_diameter", [2, 0, 1]),
            ("-height_diameter", [1, 0, 2]),
        ],
    )
    def test_ordering(
        self,
        ordering: str,
        expected_order: list[int],
        assets,
        client,
        asset_settings,
        asset_owner_org,
        standard_user,
    ):
        org = asset_owner_org

        client.force_login(user=standard_user)
        res = client.get(
            asset_settings.asset_list_url.format(organisation_id=org.id)
            + f"&standard={org.standard_key.id}&ordering={ordering}"
        )
        assert res.status_code == status.HTTP_200_OK
        data = res.json()

        id_to_idx = {str(a.uuid): i for i, a in enumerate(assets)}
        idxs = [id_to_idx[a["uuid"]] for a in data["results"]]
        assert idxs == expected_order
