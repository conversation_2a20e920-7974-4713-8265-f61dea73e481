"""
'Factory' functions for creating usable test data
"""

import datetime
import random

from django.db import models
from django.utils import timezone

from api.common.enums import OrganisationTypeEnum, StatusEnum
from api.defects.models import Standard, StandardHeader, DefectScores, DefectModelList
from api.inspections.models import JobsTree, Asset, Inspection, MapPointList, FileList, Footage, VideoFrames
from api.inspections.models.footage import ChainageUnit
from api.inspections.utilities.data_fetching import get_inspection_list
from api.organisations.models import Organisations
from api.users.models import CustomUser


def create_initial_folders(org: Organisations):
    """
    Create the root folder and an empty 'Unallocated' folder for the given organisation
    """
    now = timezone.now()
    job_name = org.full_name
    job = JobsTree.add_root(
        job_name=job_name,
        primary_org=org,
        created_date=now,
        pipe_type_sewer=org.sewer_data,
        standard_key=org.standard_key,
    )

    node = JobsTree.objects.get(pk=job.pk)
    node.add_child(
        job_name="Unallocated",
        created_date=now,
        pipe_type_sewer=org.sewer_data,
        standard_key=org.standard_key,
    )


def create_folder(
    *,
    org: Organisations,
    parent_folder: JobsTree | None = None,
    folder_name: str = "Test Folder",
    pipe_type_sewer: bool = True,
    standard: Standard | None = None,
    contractor_org: Organisations | None = None,
) -> JobsTree:
    if not parent_folder:
        parent_folder = JobsTree.get_root_nodes().get(primary_org=org)

    folder = parent_folder.add_child(
        job_name=folder_name,
        created_date=timezone.now(),
        pipe_type_sewer=pipe_type_sewer,
        standard_key=standard or org.standard_key,
        secondary_org=contractor_org,
    )
    return folder


def create_asset_owner() -> Organisations:
    standard = Standard.objects.first()
    full_name = f"test_asset_org{random.randint(10000, 99999)}"
    asset_owner_data = {
        "org_type": OrganisationTypeEnum.ASSET_OWNER,
        "full_name": full_name,
        "email_domain": f"{full_name}@vapar.co",
        "standard_key": standard,
    }

    organisation = Organisations.objects.create(**asset_owner_data)
    return organisation


def create_assets(
    *,
    org: Organisations | None = None,
    n: int = 1,
    asset_values_list: list[dict[str, str]] | None = None,
    standard: Standard | None = None,
) -> list[Asset]:
    if not org:
        org = create_asset_owner()

    assets = []
    assets_values = asset_values_list or [
        {
            "AssetID": "TEST ASSET ID",
            "UpstreamNode": "USNODE123",
            "DownstreamNode": "DSNODE456",
            "HeightDiameter": 225,
            "Material": "VC",
            "LocationStreet": "TEST LOCATION STREET",
            "LocationTown": "TEST LOCATION TOWN",
        }
    ]

    for i, _ in enumerate(range(n)):
        if len(assets_values) < n:
            assets_values.append(assets_values[0])

        asset = Asset.objects.create(organisation=org)
        standard = standard if standard else org.standard_key
        sh_asset_id = StandardHeader.objects.get(header__name="AssetID", standard=standard, header__type="asset")
        sh_upstream_node = StandardHeader.objects.get(
            header__name="UpstreamNode", standard=standard, header__type="asset"
        )
        sh_downstream_node = StandardHeader.objects.get(
            header__name="DownstreamNode", standard=standard, header__type="asset"
        )
        sh_material = StandardHeader.objects.get(header__name="Material", standard=standard, header__type="asset")
        sh_diameter = StandardHeader.objects.get(header__name="HeightDiameter", standard=standard, header__type="asset")
        sh_location_town = StandardHeader.objects.get(
            header__name="LocationTown", standard=standard, header__type="asset"
        )
        sh_location_street = StandardHeader.objects.get(
            header__name="LocationStreet", standard=standard, header__type="asset"
        )
        asset_value_uniqueness = asset.uuid
        asset.assetvalue_set.create(
            standard_header=sh_asset_id,
            value=f"{assets_values[i]['AssetID']}{asset_value_uniqueness}",
            original_value=f"{assets_values[i]['AssetID']}{asset_value_uniqueness}",
        )
        asset.assetvalue_set.create(
            standard_header=sh_upstream_node,
            value=assets_values[i]["UpstreamNode"],
            original_value=assets_values[i]["UpstreamNode"],
        )
        asset.assetvalue_set.create(
            standard_header=sh_downstream_node,
            value=assets_values[i]["DownstreamNode"],
            original_value=assets_values[i]["DownstreamNode"],
        )
        asset.assetvalue_set.create(
            standard_header=sh_material,
            value=assets_values[i]["Material"],
            original_value=assets_values[i]["Material"],
        )
        asset.assetvalue_set.create(
            standard_header=sh_diameter,
            value=assets_values[i]["HeightDiameter"],
            original_value=assets_values[i]["HeightDiameter"],
        )
        asset.assetvalue_set.create(
            standard_header=sh_location_street,
            value=assets_values[i]["LocationStreet"],
            original_value=assets_values[i]["LocationStreet"],
        )
        asset.assetvalue_set.create(
            standard_header=sh_location_town,
            value=assets_values[i]["LocationTown"],
            original_value=assets_values[i]["LocationTown"],
        )
        asset.save()
        assets.append(asset)

    return assets


def create_inspections(
    *,
    asset: Asset | None = None,
    folder: JobsTree | None = None,
    org: Organisations | None = None,
    n: int = 1,
) -> list[Inspection]:
    insp_models = create_bulk_inspections(asset, folder, org, n)
    return [Inspection.objects.get(uuid=insp["inspection_id"]) for insp in insp_models]


def create_bulk_inspections(
    asset: Asset | None = None,
    folder: JobsTree | None = None,
    target_org: Organisations | None = None,
    n: int = 1,
) -> list[dict]:
    if not asset:
        asset = create_assets(org=target_org)[0]

    if not target_org:
        target_org = asset.organisation

    if not folder:
        root_folder = JobsTree.objects.filter(primary_org=target_org, job_name="Test Asset Owner Organisation").first()
        folder = root_folder.add_child(
            job_name="TEST JOB",
            created_date=timezone.now(),
            pipe_type_sewer=True,
            standard_key=target_org.standard_key,
        )

    standard = folder.standard_key
    sh_date_captured = StandardHeader.objects.get(header__name="Date", standard=standard, header__type="inspection")
    sh_chainage = StandardHeader.objects.get(
        header__name="LengthSurveyed", standard=standard, header__type="inspection"
    )
    sh_direction = StandardHeader.objects.get(header__name="Direction", standard=standard, header__type="inspection")

    for i in range(n):
        file = FileList.objects.create(
            filename=f"test_file{i}",
            target_org=target_org,
            job_tree=folder,
            uploaded_by=CustomUser.objects.all().first(),
            upload_org=target_org,
        )
        inspection = Inspection.objects.create(asset=asset, file=file, folder=folder, status=StatusEnum.UPLOADED)
        # create inspection values
        inspection.inspectionvalue_set.create(
            standard_header=sh_date_captured,
            value=datetime.datetime.now().isoformat(),
            original_value=datetime.datetime.now().isoformat(),
        )
        inspection.inspectionvalue_set.create(
            standard_header=sh_chainage,
            value=150,
            original_value=150,
        )
        inspection.inspectionvalue_set.create(
            standard_header=sh_direction,
            value="Upstream",
            original_value="Upstream",
        )
        inspection.save()

        # To be removed with MPL deprecation
        mappointlist = MapPointList.objects.create(
            status=StatusEnum.UPLOADED,
            inspection_id=inspection.uuid,
            associated_file=file,
            standard_key=target_org.standard_key,
        )
        inspection.legacy_id = mappointlist.id
        inspection.save()

    # return inspections list from inspection model
    inspections = Inspection.objects.filter(asset=asset)
    inspection_list = [inspection.model_dump() for inspection in get_inspection_list(queryset=inspections)]

    return inspection_list


def create_video_frame(
    *,
    file: FileList | None = None,
    frame_id: int | None = None,
    target_org: Organisations | None = None,
    class_certainty: float = 0.0,
    defect_scores: DefectScores | None = None,
    defect_model: DefectModelList | None = None,
) -> VideoFrames:
    file = file or create_file_record(target_org=target_org, n_frames=0)
    frame_id = frame_id or file.videoframes_set.aggregate(num=models.Max("frame_id"))["num"] or 1
    frame = file.videoframes_set.create(
        frame_id=frame_id,
        class_certainty=class_certainty,
        defect_scores=defect_scores,
        defect_model=defect_model,
    )
    file.total_frames += 1
    file.save()
    return frame


def create_file_record(
    *,
    target_org: Organisations | None = None,
    upload_org: Organisations | None = None,
    folder: JobsTree | None = None,
    n_frames: int = 0,
) -> FileList:
    target_org = target_org or create_asset_owner()
    upload_org = upload_org or target_org
    file_record = FileList.objects.create(
        filename="test_file",
        target_org=target_org,
        upload_org=upload_org,
        job_tree=folder,
        upload_completed_time=datetime.datetime(2025, 1, 1, tzinfo=datetime.timezone.utc),
    )
    for i in range(n_frames):
        create_video_frame(file=file_record, frame_id=i + 1)
    return file_record


def create_footage(
    *,
    org: Organisations | None = None,
    video_file: FileList | None = None,
    n_keyframes: int = 0,
    chainage_unit: ChainageUnit = ChainageUnit.METRES,
) -> Footage:
    org = org or create_asset_owner()
    footage = Footage.objects.create_for_org(org, chainage_unit=chainage_unit, file=video_file)
    for i in range(n_keyframes):
        footage.keyframes.create(
            sequence_number=i,
            time_reference_milliseconds=i * 1000,
            chainage=i * 0.1,
        )
    footage.total_frames = n_keyframes
    footage.save()
    return footage
