import pytest
from rest_framework import status

from api.tests import factory
from api.inspections.models import Footage

pytestmark = pytest.mark.django_db


class TestFootageCreation:
    def test_create_without_video_file(self, asset_owner_org, service_user, service_user_key, client, footage_settings):
        res = client.post(
            footage_settings.footage_base_url,
            HTTP_X_API_KEY=service_user_key,
            HTTP_X_TARGET_ORG_ID=asset_owner_org.id,
            data={"videoFileId": None},
            content_type="application/json",
        )
        assert res.status_code == status.HTTP_201_CREATED
        res_data = res.json()
        assert res_data["videoFileId"] is None
        f_id = res_data["id"]
        footage_obj = Footage.objects.get(id=f_id)
        assert footage_obj.video_file is None
        assert footage_obj.target_org == asset_owner_org
        assert footage_obj.total_frames == 0

    def test_create_with_video_file(
        self, asset_owner_org, file_obj, service_user, service_user_key, client, footage_settings
    ):
        res = client.post(
            footage_settings.footage_base_url,
            HTTP_X_API_KEY=service_user_key,
            HTTP_X_TARGET_ORG_ID=asset_owner_org.id,
            data={"videoFileId": file_obj.id},
            content_type="application/json",
        )
        assert res.status_code == status.HTTP_201_CREATED
        res_data = res.json()
        assert res_data["videoFileId"] == file_obj.id
        f_id = res_data["id"]
        footage_obj = Footage.objects.get(id=f_id)
        assert footage_obj.video_file == file_obj
        assert footage_obj.target_org == asset_owner_org
        assert footage_obj.total_frames == 0


class TestFootageRetrieve:
    @pytest.fixture
    def footage_obj(self, asset_owner_org):
        return factory.create_footage(org=asset_owner_org, n_keyframes=10)

    def test_retrieve(self, asset_owner_org, standard_user, client, footage_settings, footage_obj):
        client.force_login(user=standard_user)
        res = client.get(footage_settings.get_footage_retrieve_url(footage_obj.id))
        assert res.status_code == status.HTTP_200_OK
        res_data = res.json()
        assert res_data["id"] == str(footage_obj.id)
        assert res_data["videoFileId"] is None
        assert res_data["totalFrames"] == footage_obj.total_frames
        assert len(res_data["keyframes"]) == 10


class TestFootageCreateDestroyKeyframes:
    @pytest.fixture
    def footage_obj(self, asset_owner_org):
        return factory.create_footage(org=asset_owner_org, n_keyframes=0)

    @pytest.fixture
    def video_file_with_frames(self, asset_owner_org):
        file = factory.create_file_record(target_org=asset_owner_org, n_frames=3)
        return file

    def test_create_keyframes(
        self, asset_owner_org, service_user, service_user_key, client, footage_settings, footage_obj
    ):
        keyframes_data = [
            {
                "timeReferenceMilliseconds": 1000,
                "isHidden": False,
                "atJoint": False,
                "hasLossOfVision": False,
                "hasTextbox": False,
                "hasTitle": False,
            },
            {
                "timeReferenceMilliseconds": 2000,
                "isHidden": False,
                "atJoint": False,
                "hasLossOfVision": False,
                "hasTextbox": False,
                "hasTitle": False,
            },
        ]
        res = client.put(
            footage_settings.get_footage_keyframes_url(footage_obj.id),
            data=keyframes_data,
            content_type="application/json",
            HTTP_X_API_KEY=service_user_key,
            HTTP_X_TARGET_ORG_ID=asset_owner_org.id,
        )
        assert res.status_code == status.HTTP_201_CREATED
        res_data = res.json()
        assert len(res_data) == 2

        footage_obj.refresh_from_db()
        assert footage_obj.total_frames == 2

        keyframes = list(footage_obj.keyframes.all())
        assert len(keyframes) == 2

        assert keyframes[0].time_reference_milliseconds == 1000
        assert keyframes[0].sequence_number == 1

        assert keyframes[1].time_reference_milliseconds == 2000
        assert keyframes[1].sequence_number == 2

    def test_create_keyframes_with_video_frames(
        self,
        asset_owner_org,
        service_user,
        service_user_key,
        client,
        footage_settings,
        footage_obj,
        video_file_with_frames,
    ):
        footage_obj.video_file = video_file_with_frames
        footage_obj.save()
        frames = list(video_file_with_frames.videoframes_set.all())

        keyframes_data = [
            {
                "timeReferenceMilliseconds": 1000,
                "chainage": 15.0,
                "isHidden": False,
                "atJoint": False,
                "hasLossOfVision": False,
                "hasTextbox": False,
                "hasTitle": False,
                "videoFrameId": frames[0].id,
            },
            {
                "timeReferenceMilliseconds": 2000,
                "chainage": 30.0,
                "isHidden": False,
                "atJoint": False,
                "hasLossOfVision": False,
                "hasTextbox": False,
                "hasTitle": False,
                "videoFrameId": frames[1].id,
            },
        ]

        res = client.put(
            footage_settings.get_footage_keyframes_url(footage_obj.id),
            data=keyframes_data,
            content_type="application/json",
            HTTP_X_API_KEY=service_user_key,
            HTTP_X_TARGET_ORG_ID=asset_owner_org.id,
        )

        assert res.status_code == status.HTTP_201_CREATED

        footage_obj.refresh_from_db()
        assert footage_obj.total_frames == 2

        keyframes = list(footage_obj.keyframes.all())
        assert len(keyframes) == 2
        assert keyframes[0].video_frame == frames[0]
        assert keyframes[0].time_reference_milliseconds == 1000
        assert keyframes[0].chainage == 15.0

        assert keyframes[1].video_frame == frames[1]
        assert keyframes[1].time_reference_milliseconds == 2000
        assert keyframes[1].chainage == 30.0
