from django.shortcuts import get_object_or_404
from rest_framework.permissions import BasePermission, IsAuthenticated as BaseIsAuthenticated
from rest_framework_api_key.permissions import BaseHasAPIKey

from api.exports.models import Export
from api.inspections.models import Inspection, FileList
from api.organisations.models import Organisations, Contractors
from api.users.models import ServiceUserApiKey

TARGET_ORG_CUSTOM_HEADER = "X-Target-Org-Id"


def _is_org_asset_owner(request) -> bool:
    return request.organisation and request.organisation.is_asset_owner


class IsAuthenticated(BaseIsAuthenticated):
    """
    Allows access only to authenticated users

    Associates the request with an organisation.
    """

    def has_permission(self, request, view):
        if not super().has_permission(request, view):
            request.organisation = None
            return False

        if request.user.is_service_user and TARGET_ORG_CUSTOM_HEADER in request.headers:
            request.organisation = get_object_or_404(Organisations, pk=request.headers[TARGET_ORG_CUSTOM_HEADER])
        else:
            request.organisation = request.user.organisation

        return True


class IsServiceUser(BasePermission):
    """
    Allows access only to service users
    """

    def has_permission(self, request, view):
        return request.user.is_service_user


class IsProductOwner(BasePermission):
    """
    Provides access to product owners
    """

    def has_permission(self, request, view):
        return request.user.is_staff


class IsStandardUser(BasePermission):
    """
    Allows access only to standard users.
    """

    def has_permission(self, request, view):
        return request.user.user_level in ("standard", "Standard")


class IsOwner(BasePermission):
    """
    Allows access only to users that own specified resource
    """

    def has_object_permission(self, request, view, obj):
        return obj.user == request.user


class IsAssetOwnerOrg(BasePermission):
    """
    Allows access only to users that are part of an organisation that is an asset owner
    """

    def has_permission(self, request, view):
        return request.organisation.is_asset_owner


class OrgCanUpload(BasePermission):
    """
    Allows access only when a users organisation has org_can_upload set to true
    """

    def has_permission(self, request, view):
        return request.organisation.org_can_upload


def has_organisation_access_asset_owner(request, organisation):
    return organisation == request.organisation


def has_organisation_access_contractor(request, organisation):
    if request.organisation == organisation:
        return True

    return Contractors.objects.filter(
        org=request.organisation,
        assetowners__org=organisation,
    ).exists()


def has_inspection_access_asset_owner(request, inspection):
    if not inspection.asset or not inspection.asset.organisation:
        return False

    asset_org = inspection.asset.organisation
    if has_organisation_access_asset_owner(request, asset_org):
        return True
    if inspection.file and has_organisation_access_asset_owner(request, inspection.file.target_org):
        return True

    return False


def has_inspection_access_contractor(request, inspection):
    if not inspection.asset or not inspection.asset.organisation:
        return False

    asset_org = inspection.asset.organisation
    if not has_organisation_access_contractor(request, asset_org):
        return False
    if not inspection.file:
        return False
    if inspection.file.upload_org == request.organisation or inspection.file.target_org == request.organisation:
        return True

    return False


def has_mappoint_access_asset_owner(request, mappointlist):
    if mappointlist.associated_file and mappointlist.associated_file.target_org:
        organisation = mappointlist.associated_file.target_org
        return has_organisation_access_asset_owner(request, organisation)

    return False


def has_mappoint_access_contractor(request, mappointlist):
    if mappointlist.associated_file and mappointlist.associated_file.upload_org:
        organisation = mappointlist.associated_file.upload_org
        return has_organisation_access_contractor(request, organisation)

    return False


class HasOrganisationAccess(BasePermission):
    """
    Allows access only to users with permitted access to specified organisations resources
    """

    def has_object_permission(self, request, view, organisation):
        if request.user.is_staff:
            return True

        if _is_org_asset_owner(request):
            return has_organisation_access_asset_owner(request, organisation)
        return has_organisation_access_contractor(request, organisation)


class HasAccessToOrgScopedObject(BasePermission):
    """
    Allows access to users with access to the organisation that the entity is connected to.

    The org fk field name can be specified as an argument, defaulting to 'organisation'.
    """

    def has_object_permission(self, request, view, obj, org_field_name="organisation"):
        if request.user.is_staff:
            return True

        org = getattr(obj, org_field_name)
        if _is_org_asset_owner(request):
            return has_organisation_access_asset_owner(request, org)
        return has_organisation_access_contractor(request, org)


class IsOrgScopedRequest(BasePermission):
    """
    Allows access to users with an organisation set in the request
    """

    def has_permission(self, request, view):
        return request.organisation is not None


class HasMapPointListAccess(BasePermission):
    """
    Allows access only to users with permitted access to specified mappointlist
    """

    def has_object_permission(self, request, view, mappointlist):
        is_asset_owner = _is_org_asset_owner(request)

        if mappointlist.inspection:
            if is_asset_owner:
                return has_inspection_access_asset_owner(request, mappointlist.inspection)
            return has_inspection_access_contractor(request, mappointlist.inspection)

        if is_asset_owner:
            return has_mappoint_access_asset_owner(request, mappointlist)
        return has_mappoint_access_contractor(request, mappointlist)


class HasInspectionAccess(BasePermission):
    """
    Allows access only to users with permitted access to specified inspection
    """

    def has_object_permission(self, request, view, inspection=None):
        if _is_org_asset_owner(request):
            return has_inspection_access_asset_owner(request, inspection)
        return has_inspection_access_contractor(request, inspection)


class HasFolderAccess(BasePermission):
    """
    Allows access only to users with permitted access to specified folder
    """

    def has_object_permission(self, request, view, folder):
        if _is_org_asset_owner(request):
            return has_organisation_access_asset_owner(request, folder.organisation)
        return has_organisation_access_contractor(request, folder.organisation)


class HasFileAccess(BasePermission):
    """
    Allows access only to users with permitted access to specified file / video
    """

    def has_object_permission(self, request, view, obj):
        if _is_org_asset_owner(request):
            return has_organisation_access_asset_owner(request, obj.target_org)
        return has_organisation_access_contractor(request, obj.target_org)


class HasServiceApiKey(BaseHasAPIKey):
    model = ServiceUserApiKey


class RelatesToExportBelongingToOrg(BasePermission):
    """
    Allows access only if the export exists, and belongs to the requesting org
    """

    def has_permission(self, request, view):
        get_object_or_404(Export, id=view.kwargs.get("uuid"), target_org=request.organisation)
        return True


class RelatesToInspectionAccessibleByOrg(BasePermission):
    """
    Allows access only if the inspection exists, and is accessible by the requesting org
    """

    def has_permission(self, request, view):
        inspection = get_object_or_404(Inspection, uuid=view.kwargs.get("uuid"))
        return HasInspectionAccess().has_object_permission(request, view, inspection)


class RelatesToVideoFileAccessibleByOrg(BasePermission):
    """
    Allows access only if the video file exists, and is accessible by the requesting org
    """

    def has_permission(self, request, view):
        video = get_object_or_404(FileList, pk=view.kwargs.get("id"))
        return HasFileAccess().has_object_permission(request, view, video)
