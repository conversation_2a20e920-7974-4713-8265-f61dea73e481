import csv
from datetime import timezone, datetime
import json
import operator
import re
from typing import Any, Dict, List, Optional, Union

from django.db.models import CharField, ExpressionWrapper, F, IntegerField, Q

from api.actions.models import AuditList
from api.inspections.models import MapPointList, VideoFrames
from api.recommendations.models import RepairRecommendation
from api.recommendations.tasks.helpers import (
    get_custom_row,
    get_rr_data,
    get_uploaded_org_type,
    send_rr_email_to_client,
    upload_csv,
)
from api.recommendations import analytics
from api.users.models import CustomUser


def _process_inspections(inspections: List[MapPointList]) -> List[Dict[str, Any]]:
    processed = []

    for inspection in inspections:
        survey = {}
        survey["vapar_id"] = inspection.pk
        # pk mappoint
        survey["asset_id"] = inspection.asset_id

        x = ""
        y = ""
        if inspection.geometry is not None:
            x = str(inspection.geometry.x)
            y = str(inspection.geometry.y)
        survey["latitude"] = y
        survey["longitude"] = x

        survey["location"] = inspection.name
        location = survey["location"].split(",")
        # format of location has street name, followed by a comma, followed by suburb
        if len(location) > 1:
            survey["loc_street"] = location[0].strip()
            survey["loc_suburb"] = location[1].strip()
        else:
            survey["loc_street"] = "N/A"
            survey["loc_suburb"] = "N/A"

        survey["length_inspected"] = inspection.chainage
        survey["length_inspected num"] = inspection.chainage
        # m-chainage
        survey["structural_grade"] = inspection.condition_rating
        survey["service_grade"] = inspection.service_condition_rating
        # m-condition/st rating
        survey["date_captured"] = inspection.date_captured
        survey["diameter"] = inspection.diameter
        survey["video_file"] = inspection.associated_file.filename  # type: ignore
        # fileist-name
        survey["material"] = inspection.material
        survey["start_node"] = inspection.start_node
        survey["end_node"] = inspection.end_node
        survey["direction"] = inspection.direction
        survey["inspection_notes"] = inspection.inspection_notes

        survey["observations"] = []
        survey["total_str_score"] = 0
        survey["total_ser_score"] = 0
        survey["peak_str_score"] = 0
        survey["peak_ser_score"] = 0

        frames = (
            VideoFrames.objects.prefetch_related("defect_scores")
            .select_related("parent_video__mappointlist")
            .filter(parent_video__mappointlist=inspection, defect_scores__isnull=False)
            .exclude(Q(defect_scores__is_shown=False) | Q(is_hidden=True))
            .order_by("chainage_number", "pk")
            .annotate(
                defect_class=ExpressionWrapper(F("defect_scores__defect_description"), output_field=CharField()),
                defect_code=ExpressionWrapper(F("defect_scores__defect_code"), output_field=CharField()),
                defect_str_score=ExpressionWrapper(F("defect_scores__structural_score"), output_field=IntegerField()),
                defect_ser_score=ExpressionWrapper(F("defect_scores__service_score"), output_field=IntegerField()),
            )
        )

        for frame in frames:
            observation = {}
            observation["frame_id"] = frame.pk
            observation["time_ref"] = frame.time_reference
            observation["frame_class"] = frame.defect_class.replace(">", "is greater than").replace(  # type: ignore
                "<", "is less than"
            )
            # defect scores table-> class description(defect class)
            observation["frame_chainage"] = frame.chainage_number
            observation["frame_chainage_num"] = frame.chainage_number
            try:
                observation["remarks"] = frame.remarks
            except Exception:
                observation["remarks"] = ""

            try:
                observation["repair_category"] = frame.defect_scores.repair_category  # type: ignore
            except Exception:
                observation["repair_category"] = ""  # checked

            observation["repair_priority"] = frame.defect_scores.repair_priority  # type: ignore

            try:
                observation["frame_code"] = frame.defect_scores.defect_code  # type: ignore
            except Exception:
                observation["frame_code"] = ""  # checked

            # defect code
            observation["structural_score"] = frame.defect_str_score if frame.defect_str_score else 0  # type: ignore
            observation["service_score"] = frame.defect_ser_score if frame.defect_ser_score else 0  # type: ignore
            observation["frame_score"] = max(observation["structural_score"], observation["service_score"])
            if frame.at_joint:
                observation["at_joint"] = frame.at_joint
            if frame.at_clock is not None:
                observation["ClockRefAtFrom"] = frame.at_clock
                if frame.to_clock is not None:
                    observation["ClockRefTo"] = frame.to_clock
            if frame.quantity1_units == "%":
                observation["percentage"] = frame.quantity1_value
            elif frame.quantity2_units == "%":
                observation["percentage"] = frame.quantity2_value
            if frame.quantity1_value and frame.quantity1_units != "%":
                observation["dimension1"] = frame.quantity1_value
            if frame.quantity2_value and frame.quantity2_units != "%":
                if "dimension1" in observation:
                    observation["dimension2"] = frame.quantity2_value
                else:
                    observation["dimension1"] = frame.quantity2_value
            # skip
            survey["total_str_score"] += int(observation["structural_score"])
            survey["total_ser_score"] += int(observation["service_score"])
            survey["peak_str_score"] = max(survey["peak_str_score"], int(observation["structural_score"]))
            survey["peak_ser_score"] = max(survey["peak_ser_score"], int(observation["service_score"]))

            survey["observations"].append(observation)

        try:
            pipe_len = float(survey["length_inspected"])  # type: ignore
        except ValueError:
            pipe_len = 0.0

        survey["mean_str_score"] = survey["total_str_score"] / pipe_len if pipe_len > 0.0 else 0.0
        survey["mean_ser_score"] = survey["total_ser_score"] / pipe_len if pipe_len > 0.0 else 0.0

        processed.append(survey)

    return processed


def convert_float(value: Union[int, str, float]) -> float:
    if isinstance(value, str):
        try:
            return float(re.sub(r"[^\d\.]", "", value))
        except ValueError:
            return 0.0

    if isinstance(value, int):
        return float(value)

    if isinstance(value, float):
        return value

    return 0.0


def generate_repair_recommendations(surveys: List[Dict]) -> List[Dict]:
    rep_recs = []
    for survey in surveys:
        whole_rec = False
        patches = []
        roots = []
        blockage_buildup = []
        blockages = []
        digup = []
        digupdetail = {}
        non_service_observations = []
        user_params_text = MapPointList.objects.get(id=survey["vapar_id"]).associated_file.target_org.repair_param

        try:
            user_params = json.loads(user_params_text)
        except Exception:
            user_params = {
                "Minimum roots class": 15,
                "Debris build up class": 15,
                "Debris build up length m": 1,  # metres
                "Debris single instance class": 20,
                "Patch if score over length >=": 50,
                "Patch length for scoring m": 1,  # metres (for measuring gap between sections of patches)
                "Maximum number of patches over distance": 2,
                "Maximum distance for patch": 30,  # metres (e.g. if more than 2 patchees over 30m, may as well reline)
                "Maximum number of patches in total": 3,
            }

        survey["observations"].sort(key=operator.itemgetter("frame_chainage_num"))

        for observation in survey["observations"]:
            sub_class = observation.get("repair_category", "")

            # if it's a service defect, add to the relevant list
            if sub_class in ["Roots", "Debris", "Digup"]:
                if sub_class == "Roots":
                    if observation["repair_priority"] >= user_params["Minimum roots class"]:
                        roots.append(observation["frame_chainage_num"])
                elif sub_class == "Debris":
                    if int(observation["repair_priority"]) >= user_params["Debris build up class"]:
                        if int(observation["repair_priority"]) < user_params["Debris single instance class"]:
                            blockage_buildup.append(observation["frame_chainage_num"])
                        else:
                            blockages.append(observation["frame_chainage_num"])
                else:
                    digupdetail[observation["frame_chainage_num"]] = observation["frame_class"]
                    digup.append(digupdetail)
            else:
                non_service_observations.append(observation)

        # for each section of the pipe, figure out if blockage buildup is found, patch logic
        # is met, or whole pipe repair is required

        # check per (gradual) blockage if there is other blockages in close proximity

        for observation in blockage_buildup:
            section_list = [
                blockage
                for blockage in blockage_buildup
                if (blockage - observation > float(user_params["Debris build up length m"]))
            ]
            blockages.append(observation)

        #####################################################
        # above root/debris treated, patches below check per defect if sum of scores over
        # length is > patch required score and add section to patching list

        for observation in non_service_observations:
            # ignore defect if we've already signaled this section for patching
            # TODO: consolidate close patches into a single patch
            if len(patches) and (
                patches[-1] + float(user_params["Patch length for scoring m"])
                > float(observation["frame_chainage_num"])
            ):
                continue

            section_list = [
                x
                for x in non_service_observations
                if (x["frame_chainage_num"] >= observation["frame_chainage_num"])
                and (
                    (x["frame_chainage_num"] - observation["frame_chainage_num"])
                    < float(user_params["Patch length for scoring m"])
                )
            ]

            sum_nsd = 0
            for nsd in section_list:
                # sum_nsd += defect_scores[nsd['frame class']]['score']
                sum_nsd += observation["structural_score"]
            if sum_nsd >= user_params["Patch if score over length >="]:
                patches.append(float(observation["frame_chainage_num"]))

        # now that we have a list of patches, check if we should bother patching, or
        # just do whole pipe repair

        if len(patches) > user_params["Maximum number of patches in total"]:
            whole_rec = True
        elif len(patches) >= user_params["Maximum number of patches over distance"]:
            for patch in patches:
                section_list = [x for x in patches if (x - patch < float(user_params["Maximum distance for patch"]))]
                if len(section_list) > user_params["Maximum number of patches over distance"]:
                    whole_rec = True
                    break

        survey["patch_counted"] = len(patches)
        survey["recommendations"] = ""

        if whole_rec:
            survey["whole_rec_required"] = True
            survey["recommendations"] = "Whole of pipe repair"
            survey["patch_counted"] = str(len(patches))
            survey["digup"] = False
            survey["digup_detail"] = ""
            if len(digup) > 0:
                survey["digup"] = True

                if survey["recommendations"] != "":
                    survey["recommendations"] += "| "
                survey["recommendations"] += "Dig up at "
                survey["digup_detail"] = "Dig up at "

                for key, value in digup[0].items():
                    survey["recommendations"] += str(key) + ", "
                    survey["digup_detail"] += str(key) + " " + str(value)[:-1] + ";"
        else:
            survey["whole_rec_required"] = False

            if len(patches) > 0:
                survey["patch_required"] = True
                survey["recommendations"] = "Patch repair at "
                for patch in patches:
                    survey["recommendations"] += str(patch) + ", "
            else:
                survey["patch_required"] = False
            if len(blockages) > 0:
                survey["clean_required"] = True
                if survey["recommendations"] != "":
                    survey["recommendations"] += "| "
                survey["recommendations"] += "Clear pipe at "
                for blockage in blockages:
                    survey["recommendations"] += str(blockage) + ", "
            else:
                survey["clean_required"] = False
            if len(roots) > 0:
                survey["root_treat"] = True
                if survey["recommendations"] != "":
                    survey["recommendations"] += "| "
                survey["recommendations"] += "Root treatment at "
                for root in roots:
                    survey["recommendations"] += str(root) + ", "
            else:
                survey["root_treat"] = False
            if len(digup) > 0:
                survey["digup"] = True

                if survey["recommendations"] != "":
                    survey["recommendations"] += "| "
                survey["recommendations"] += "Dig up at "
                survey["digup_detail"] = "Dig up at "

                for key, value in digup[0].items():
                    survey["recommendations"] += str(key) + ", "
                    survey["digup_detail"] += str(key) + " " + str(value)[:-1] + "; "
            else:
                survey["digup"] = False
                survey["digup_detail"] = ""

        if survey["recommendations"] == "":
            survey["no_action"] = True
            survey["recommendations"] = "No immediate action required"
        else:
            survey["no_action"] = False
        survey["user_params"] = user_params

        rep_recs.append(survey)

    return rep_recs


def update_or_create_recommendation(inspection: Dict[str, Any], user: CustomUser):
    now = datetime.now(timezone.utc)

    summary = {
        "id": inspection["vapar_id"],
        "location": inspection["location"],
        "video_file": inspection["video_file"],
        "asset_id": inspection["asset_id"],
        "start_node": inspection["start_node"],
        "end_node": inspection["end_node"],
        "direction": inspection["direction"],
        "length_inspected": inspection["length_inspected"],
        "recommendations": inspection["recommendations"],
    }

    if inspection["whole_rec_required"]:
        inspection["root_treat"] = False
        inspection["clean_required"] = False
        inspection["patch_counted"] = 0
        inspection["patch_required"] = False
        inspection["digup"] = inspection["digup"]
        inspection["digup_detail"] = inspection.get("digup_detail", "empty")
        inspection["no_action"] = False

    try:
        rr = RepairRecommendation.objects.get(target_id=inspection["vapar_id"])
    except RepairRecommendation.DoesNotExist:
        # creating
        rr = RepairRecommendation()
        description = "Bulk Run RR"
        AuditList.objects.create(
            event_type="Create RR",
            table="repairrecommendation",
            row_id=inspection["vapar_id"],
            column="Multi",
            description=description,
            date_of_modification=now,
            user=user,
        )

    rr.inspection_parameters = inspection["user_params"]
    rr.action_summary = inspection["recommendations"]
    rr.root_treatment = inspection.get("root_treat", False)
    rr.cleaning_required = inspection["clean_required"]
    rr.patches_counted = inspection["patch_counted"]
    rr.patching_details = inspection["patch_required"]
    rr.full_relining = inspection["whole_rec_required"]
    rr.dig_up = inspection["digup"]
    rr.dig_up_details = inspection["digup_detail"]
    rr.no_immediate_action = inspection["no_action"]

    rr.updated_at = now
    rr.updated_by = user

    try:
        target_inspection = MapPointList.objects.get(id=inspection["vapar_id"])
    except MapPointList.DoesNotExist:
        return None

    rr.target = target_inspection
    rr.save()

    analytics.send_repair_recommendation_created_event(rr)

    return summary


def run_many_repair_recommendations(ids: List[int], user: CustomUser) -> tuple[list[dict], bool]:
    """
    :param ids: List of map point list ids
    :param user: User who triggered the update
    :returns: A list of RR summary dicts and a boolean which is True if there was an error
    """
    error = False
    if len(ids) == 0:
        return [], True

    inspections = MapPointList.objects.filter(id__in=ids, deleted_at__isnull=True)

    if inspections.filter(status="Uploaded").exists():
        return [], True

    inspections = _process_inspections(inspections)  # type: ignore
    recommendations = generate_repair_recommendations(inspections)

    outlist = [update_or_create_recommendation(i, user) for i in inspections]
    if outlist:
        outlist.append({"options": recommendations[-1]["user_params"]})

    return outlist, error


def generate_repair_recommendation_csv(
    ids: List[int],
    org_name: str,
    is_asset_owner: bool,
    headerlist,
    custom_repair_header,
    filename: Optional[str] = None,
):
    data = get_rr_data(ids, is_asset_owner)
    if filename is None:
        trimed_org_name = org_name.replace(" ", "_") if " " in org_name else org_name
        filename = trimed_org_name + "_" + str(datetime.now()) + "_rr.csv"
        filename = filename.replace(" ", "_")

    with open(filename, mode="w") as large_csv:
        writer = csv.writer(large_csv, delimiter=",", quotechar='"', quoting=csv.QUOTE_MINIMAL)
        writer.writerow(headerlist)

        for row in data:
            customrow = get_custom_row(row.target_id)
            contract_valuesList = []
            owner_valuesList = []

            for key in custom_repair_header:
                try:
                    contract_values = customrow["contractor"][key]
                except Exception:
                    contract_values = ""  # deal with empty custom rr
                contract_valuesList.append(contract_values)
                try:
                    owner_values = customrow["owner"][key]
                except Exception:
                    owner_values = ""  # deal with empty custom rr
                owner_valuesList.append(owner_values)

            point = MapPointList.objects.get(id=row.target_id)

            # We don't want to write the row unless the
            # inspection status is "Reviewed" or later
            if point.status in ["Planned", "Uploaded"]:
                continue

            frequency2 = getattr(row, "inspection_frequency", "")

            valuelist_suggestion = [
                point.pk,
                point.name,
                point.upstream_node,
                point.downstream_node,
                point.direction,
                point.condition_rating,
                point.service_condition_rating,
                point.date_captured,
                point.chainage,
                point.diameter,
                point.material,
                point.associated_file.filename,
                point.associated_file.job_tree,
                point.inspection_notes,
                point.status,
                frequency2,
                row.asset_id,
                row.action_summary,
                row.no_immediate_action,
                row.root_treatment,
                row.cleaning_required,
                row.patching_details,
                row.patches_counted,
                row.dig_up,
                row.full_relining,
                "NA",
                "NA",
                "NA",
                "NA",
                "AUTOMATED SUGGESTION",
            ]
            valuelist_suggestion[25:25] = ["NA"] * len(custom_repair_header)

            valuelist_contractor = [
                point.pk,
                point.name,
                point.upstream_node,
                point.downstream_node,
                point.direction,
                point.condition_rating,
                point.service_condition_rating,
                point.date_captured,
                point.chainage,
                point.diameter,
                point.material,
                point.associated_file.filename,
                point.associated_file.job_tree,
                point.inspection_notes,
                point.status,
                frequency2,
                row.asset_id,
                row.c_action_summary,
                row.c_no_immediate_action,
                row.c_root_treatment,
                row.c_cleaning_required,
                row.c_patching_details,
                row.c_patches_counted,
                row.c_dig_up,
                row.c_full_relining,
                row.risk_likelihood.name if row.risk_likelihood else "",
                row.likelihood_comment,
                row.risk_consequence.name if row.risk_consequence else "",
                row.consequence_comment,
                "CONTRACTOR RECOMMENDATION",
            ]
            valuelist_contractor[25:25] = contract_valuesList

            valuelist_owner = [
                point.pk,
                point.name,
                point.upstream_node,
                point.downstream_node,
                point.direction,
                point.condition_rating,
                point.service_condition_rating,
                point.date_captured,
                point.chainage,
                point.diameter,
                point.material,
                point.associated_file.filename,
                point.associated_file.job_tree,
                point.inspection_notes,
                point.status,
                frequency2,
                row.asset_id,
                row.o_action_summary,
                row.o_no_immediate_action,
                row.o_root_treatment,
                row.o_cleaning_required,
                row.o_patching_details,
                row.o_patches_counted,
                row.o_dig_up,
                row.o_full_relining,
                row.risk_likelihood.name if row.risk_likelihood else "",
                row.likelihood_comment,
                row.risk_consequence.name if row.risk_consequence else "",
                row.consequence_comment,
                "DECISION",
            ]
            valuelist_owner[25:25] = owner_valuesList

            if is_asset_owner:
                writer.writerow(valuelist_suggestion)
                if get_uploaded_org_type(row.target_id):
                    writer.writerow(valuelist_contractor)
                writer.writerow(valuelist_owner)
            else:
                writer.writerow(valuelist_suggestion)
                writer.writerow(valuelist_contractor)
                if get_uploaded_org_type(row.target_id):
                    writer.writerow(valuelist_owner)

    return filename


def repair_recommendations_csv_task(
    ids: List[int],
    org_name: str,
    user_email: str,
    is_asset_owner: bool,
    headerlist,
    custom_repair_header,
):
    csvfile = generate_repair_recommendation_csv(ids, org_name, is_asset_owner, headerlist, custom_repair_header)

    sas_read_token = upload_csv(csvfile)
    send_rr_email_to_client(csvfile, sas_read_token, user_email)
