FROM python:3.10-slim-buster

# Set environment varibles
ENV PYTHONDONTWRITEBYTECODE 1
ENV PYTHONUNBUFFERED 1

ARG DB_HOST
ARG DB_NAME
ARG DB_USER
ARG DB_PORT

ARG BITBUCKET_VAPAR_REPO_TOKEN

# set work directory
WORKDIR /app

COPY . .

RUN apt-get update  \
    && apt-get install -y openjdk-11-jre-headless

RUN set -ex \
   && RUN_DEP=" \
    pango1.0-tests \
    gdal-bin \
    " \
    && apt-get update -y && apt-get install -y --no-install-recommends $RUN_DEP \
    && pip install --upgrade pip \
    && rm -rf /var/lib/apt/lists/*

RUN set -ex \
    && BUILD_DEPS=" \
    #libpcre3-dev \
    #libpq-dev \
    " \
    && apt update -q \
    && apt-get upgrade -y \
    && apt-get install libgdk-pixbuf2.0-0 -y \
    && apt-get install libcairo2-dev -y \
    && apt-get install libpango1.0-0 -y \
    && apt-get upgrade -y \
    && apt-get install binutils libproj-dev gdal-bin -y \
    && apt-get purge -y --auto-remove -o APT::AutoRemove::RecommendsImportant=false $BUILD_DEPS \
    && rm -rf /var/lib/apt/lists/*

RUN apt-get update -y && \
    apt-get install -y postgresql-client

RUN apt-get upgrade -y \
    && apt-get install libpangocairo-1.0-0 && apt-get install shared-mime-info -y \
    && apt-get install python3-pip python3-cffi python3-brotli libpango-1.0-0 libharfbuzz0b libpangoft2-1.0-0 -y \
    && apt-get update --fix-missing \
    && apt-get install default-jre -y \
    && apt-get install -y git

RUN pip install --upgrade pip
RUN pip install pipenv psycopg2-binary drf-extra-fields django-treebeard azure-storage-blob
RUN pip install gunicorn django-cors-headers


# Copy in your requirements file
ADD ./requirements.txt /usr/src/app/requirements.txt
RUN pip install -r requirements.txt

EXPOSE 8000

# Add any static environment variables needed by Django or your settings file here:
ENV DJANGO_SETTINGS_MODULE=config.settings

CMD ["sh", "-e", "entrypoint.sh"]
