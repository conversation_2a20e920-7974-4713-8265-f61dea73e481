image:
  name: vaparbitbucket.azurecr.io/api/tests:latest
  username: $AZURE_CONTAINER_REGISTRY_USERNAME
  password: $AZURE_CONTAINER_REGISTRY_PASSWORD

definitions:
  lint: &lint
    name: Lint code
    image: python:3.11-slim
    caches:
      - pip
    script:
      - pip install ruff==0.3.7
      - ruff check api/

    test: &test
      name: Test
      caches:
        - pip
      script:
        - if [ -requirements.txt ]; then pip install -r requirements.txt; fi
        - pip install pytest==7.4.2
        - pip install pytest-asyncio==0.23.5.post1
        - pip install pytest-django==4.5.2
        - chmod u+x resources/NASSCO/NasscoConsoleValidator-1.8.1-linux-x64
        - pytest -v api/tests/*
      services:
        - postgres
        - redis

  services:
    postgres:
      image: postgis/postgis:12-3.4
      variables:
        POSTGRES_USER: $DB_USER
        POSTGRES_PASSWORD: $DB_PASSWORD
    redis:
      image: redis

pipelines:
  pull-requests:
    '**':
      - parallel:
        - step: *lint
        - step: *test

  branches:
    development:
        - parallel:
          - step: *lint
          - step: *test
        - step:
            name: Deploy to development
            deployment: development
            trigger: automatic
            caches:
              - pip
            services:
              - docker
            script:
              - export DOCKER_IMAGE_TAG="dev"
              - export BUILD_SOURCEVERSION=${BITBUCKET_COMMIT}
              - export AZURE_TENANT=${DEV_AZURE_TENANT}
              - export AZURE_CLIENT_ID=${DEV_AZURE_CLIENT_ID}
              - export AZURE_SECRET=${DEV_AZURE_CLIENT_SECRET}
              - export AZURE_SUBSCRIPTION_ID=${DEV_AZURE_SUBSCRIPTION_ID}
              - export AZURE_RESOURCE_GROUP=${DEV_AZURE_RESOURCE_GROUP}
              - export AKS_CLUSTER_NAME=${DEV_AKS_CLUSTER_NAME}
              - export AKS_NAMESPACE=${DEV_AKS_NAMESPACE}

              - apt-get update && apt-get install -y make git curl lsb-release

              - make -e docker-login
              - make -e docker-build
              - make -e docker-push

              - make -e install-az
              - make -e install-kubectl

              - make -e azure-login
              - make -e azure-aks-get-credentials
              - make -e azure-aks-restart
    release/*:
        - parallel:
          - step: *lint
          - step: *test